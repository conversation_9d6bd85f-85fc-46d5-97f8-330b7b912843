# PropBolt - Clean Project Structure

## 🏗️ **FINAL CLEAN ARCHITECTURE**

```
propbolt/
├── api/                      # Go API Server (api.propbolt.com)
│   ├── main.go              # Main API server entry point
│   ├── go.mod               # Go module dependencies
│   ├── go.sum               # Go dependency checksums
│   ├── app.yaml             # Google Cloud App Engine config
│   ├── .env                 # API environment variables
│   ├── propbolt-service-account-key.json  # GCP service account
│   ├── autocomplete/        # Address autocomplete endpoints
│   ├── details/             # Property details endpoints
│   ├── search/              # Property search endpoints
│   ├── zestimate/           # Property value estimation
│   ├── propbolthelper/      # Helper utilities
│   └── utils/               # Common utilities
│
├── data/                    # Python Data API (data.propbolt.com)
│   ├── main.py              # Flask application entry point
│   ├── requirements.txt     # Python dependencies
│   ├── app.yaml             # Google Cloud App Engine config
│   ├── .env                 # Data API environment variables
│   ├── propbolt-service-account-key.json  # GCP service account
│   └── README.md            # Data API documentation
│
├── dashboard/               # Next.js Dashboard (go.propbolt.com)
│   ├── src/                 # Source code
│   │   ├── app/            # Next.js App Router
│   │   ├── components/     # React components
│   │   ├── lib/            # Utilities and configurations
│   │   └── hooks/          # Custom React hooks
│   ├── public/             # Static assets
│   ├── app.yaml            # Google Cloud App Engine config
│   ├── .env.local          # Dashboard environment variables
│   ├── package.json        # Node.js dependencies
│   ├── next.config.js      # Next.js configuration
│   ├── tailwind.config.js  # Tailwind CSS configuration
│   ├── tsconfig.json       # TypeScript configuration
│   └── postcss.config.js   # PostCSS configuration
│
├── landing/                # Next.js Landing Page (propbolt.com)
│   ├── src/                # Source code
│   │   └── app/           # Next.js App Router
│   ├── public/            # Static assets
│   ├── app.yaml           # Google Cloud App Engine config
│   ├── .env.local         # Landing environment variables
│   ├── package.json       # Node.js dependencies
│   ├── next.config.js     # Next.js configuration
│   ├── tailwind.config.js # Tailwind CSS configuration
│   ├── tsconfig.json      # TypeScript configuration
│   └── postcss.config.js  # PostCSS configuration
│
├── scripts/               # Database and utility scripts
│   ├── init-database.sql # Database initialization
│   ├── init-db.js        # Database setup script
│   ├── create-admin.js   # Admin user creation
│   └── simple-init.sql   # Simple database setup
│
├── deploy-gcp.sh         # Google Cloud deployment script
├── dispatch.yaml         # App Engine routing configuration
└── PROJECT_STRUCTURE.md  # This file
```

## 🎯 **SERVICES OVERVIEW**

### **API Service (Priority #1)**
- **Domain**: api.propbolt.com
- **Technology**: Go + Gin Framework
- **Purpose**: High-performance REST API for 3rd party integrations
- **Scaling**: Enterprise auto-scaling (2-50 instances)

### **Data API Service (Priority #2)**
- **Domain**: data.propbolt.com
- **Technology**: Python + Flask
- **Purpose**: Data processing, analytics, and bulk operations
- **Features**: Property analytics, data export, bulk processing
- **Scaling**: Auto-scaling (1-100 instances)

### **Dashboard Service (Priority #3)**
- **Domain**: go.propbolt.com
- **Technology**: Next.js 14 + React
- **Purpose**: User and Admin portal
- **Features**: User management, billing, API usage, land searching

### **Landing Service (Priority #4)**
- **Domain**: propbolt.com
- **Technology**: Next.js 14 + React
- **Purpose**: Marketing and sign-up page
- **Features**: Authentication redirect, service information

## 🚀 **DEVELOPMENT WORKFLOW**

### **Local Development**
```bash
# API Server
cd api && go run main.go

# Data API Server
cd data && python main.py

# Dashboard
cd dashboard && npm run dev

# Landing Page
cd landing && npm run dev
```

### **Production Deployment**
```bash
./deploy-gcp.sh
```

## ✅ **CLEANUP COMPLETED**

All unnecessary files have been removed:
- 13 outdated documentation files
- 4 unused script files
- 4 individual service start scripts
- 2 unused directories (shared/, cloud-sql-proxy/)

The project is now clean and ready for enterprise configuration.

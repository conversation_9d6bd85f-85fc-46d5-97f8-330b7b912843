# PropBolt API Dependencies - Core Framework
annotated-types==0.7.0 ; python_version >= "3.8" and python_version < "4.0"
anyio==3.7.1 ; python_version >= "3.8" and python_version < "4.0"
certifi==2025.4.26 ; python_version >= "3.8" and python_version < "4.0"
click==8.1.8 ; python_version >= "3.8" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.8" and python_version < "4.0" and (platform_system == "Windows" or sys_platform == "win32")
exceptiongroup==1.3.0 ; python_version >= "3.8" and python_version < "3.11"
fastapi==0.104.1 ; python_version >= "3.8" and python_version < "4.0"
h11==0.16.0 ; python_version >= "3.8" and python_version < "4.0"
httpcore==1.0.9 ; python_version >= "3.8" and python_version < "4.0"
httptools==0.6.4 ; python_version >= "3.8" and python_version < "4.0"
httpx==0.28.1 ; python_version >= "3.8" and python_version < "4.0"
idna==3.10 ; python_version >= "3.8" and python_version < "4.0"
jsonpointer==3.0.0 ; python_version >= "3.8" and python_version < "4.0"
pydantic-core==2.27.2 ; python_version >= "3.8" and python_version < "4.0"
pydantic==2.10.6 ; python_version >= "3.8" and python_version < "4.0"
python-dotenv==1.0.1 ; python_version >= "3.8" and python_version < "4.0"
python-multipart==0.0.6 ; python_version >= "3.8" and python_version < "4.0"
pyyaml==6.0.2 ; python_version >= "3.8" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.8" and python_version < "4.0"
starlette==0.27.0 ; python_version >= "3.8" and python_version < "4.0"
typing-extensions==4.13.2 ; python_version >= "3.8" and python_version < "4.0"
uvicorn==0.24.0.post1 ; python_version >= "3.8" and python_version < "4.0"
uvloop==0.21.0 ; python_version >= "3.8" and python_version < "4.0" and sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy"
watchfiles==0.24.0 ; python_version >= "3.8" and python_version < "4.0"
websockets==13.1 ; python_version >= "3.8" and python_version < "4.0"

# Database and API Key Management Dependencies
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.13.0

# Additional utilities
requests>=2.31.0

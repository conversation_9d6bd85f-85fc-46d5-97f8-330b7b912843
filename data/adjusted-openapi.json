{"openapi": "3.1.0", "info": {"title": "Property APIs", "version": "1.0"}, "servers": [{"url": "https://data.propbolt.com", "description": "Production server"}], "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key"}}}, "security": [{"ApiKeyAuth": []}], "paths": {"/v2/PropertyDetail": {"post": {"summary": "Property Detail API", "description": "Comps, Mortgages, Mailing Addresses, Property Sales History & More!", "operationId": "property-detail-api-1", "parameters": [{"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "The \"id\" property from the Property Search API result objects. If using this unique property ID for Property Detail, then no other fields are required."}, "comps": {"type": "boolean", "description": "Set to true if you would like to have houses nearby with conferable valuations returned as part of the response. ***Includes RealEstateAPI's special AVM for the property***", "default": false}, "exact_match": {"type": "boolean", "description": "only return matches that match your exact address or address parts, no fuzzy matching"}, "address": {"type": "string", "description": "A fully formatted address with the following format: 123 Main St, Arlington VA 22205. When this field is provided and is in the valid format, no other fields are required"}, "house": {"type": "string", "description": "House number field is to be used with the street, city, state, and zip fields (& unit if applicable)"}, "unit": {"type": "string", "description": "House unit number (for Apt. #'s, Suite #'s, etc.)"}, "street": {"type": "string", "description": "The street name of the property (e.g. Main St)"}, "city": {"type": "string", "description": "The city where the property is located"}, "state": {"type": "string", "pattern": "^[A-Z]{2}$", "minLength": 2, "maxLength": 2, "description": "Two-letter state code (e.g. VA = Virginia)"}, "county": {"type": "string", "description": "(*This field is not required for a fully formatted address*) The county where your search property is located."}, "zip": {"type": "string", "pattern": "^[0-9]{5}$", "minLength": 5, "maxLength": 5, "description": "5-digit ZIP code"}, "apn": {"type": "string", "description": "The assessor's parcel number (APN) is a unique identifier for an address/parcel & is particularly helpful when looking up Land Parcels without House Numbers"}, "fips": {"type": "string", "pattern": "^[0-9]{5}$", "minLength": 5, "maxLength": 5, "description": "5-digit FIPS county code"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"examples": {"Result": {"value": "{\n  input: {\n    house: '17',\n    street: 'Topeka Pass',\n    city: 'Willingboro',\n    state: 'NJ',\n    zip: '08046'\n\t},\n  data: {\n      ownerInfo: [Object],\n      deedInLieu: false,\n      lotInfo: [Object],\n      preForeclosure: false,\n      absenteeOwner: false,\n      warrantyDeed: false,\n      lastSaleDate: '2021-12-16',\n      id: *********,\n      currentMortgages: [Array],\n      taxLien: false,\n      adjustableRate: false,\n      vacant: false,\n      mobileHome: false,\n      sheriffsDeed: false,\n      spousalDeath: false,\n      equityPercent: 100,\n      estimatedEquity: 349267,\n      inStateAbsenteeOwner: false,\n      reapi_loaded_at: '0',\n      priorId: **********,\n      death: false,\n      cashBuyer: true,\n      mortgageHistory: [],\n      ownerOccupied: true,\n      outOfStateAbsenteeOwner: false,\n      quitClaim: false,\n      deathTransfer: false,\n      bankOwned: false,\n      propertyInfo: [Object],\n      last_sale: [Object],\n      estimatedValue: 349267,\n      lien: false,\n      propertyType: 'SFR',\n      corporateOwned: false,\n      openMortgageBalance: 0,\n      privateLender: false,\n      taxInfo: [Object],\n      \"demographics\" : {\n\t\t\t\t\"medianIncome\" : \"130336\",\n \t\t\t\t\"suggestedRent\" : \"3010\",\n\t\t\t\t\"fmrYear\": \"2023\",\n\t\t\t\t\"fmrEfficiency\" : \"1750\",\n\t\t\t\t\"fmrOneBedroom\" : \"1780\",\n\t\t\t\t\"fmrTwoBedroom\" : \"2020\",\n\t\t\t\t\"fmrThreeBedroom\" : \"2530\",\n\t\t\t\t\"fmrFourBedroom\" : \"3010\",\n\t\t\t\t\"hudAreaName\" : \"Washington-Arlington-Alexandria, DC-VA-MD HUD Metro FMR Area\",\n  \t\t\t\"hudAreaCode\" : \"METRO47900M47900\"\n\t\t\t},\n      saleHistory: [Array],\n      cashSale: false,\n      foreclosureInfo: [],\n      inherited: false,\n      trusteeSale: false,\n      lastSalePrice: '350000',\n      equity: 274800,\n      comps: [],\n      reapiAvm: null\n    },\n    statusCode: 200,\n    statusMessage: 'Success',\n    credits: 0,\n    live: true,\n    requestExecutionTimeMS: '40ms',\n    propertyLookupExecutionTimeMS: '40ms',\n    compsLookupExecutionTimeMS: null\n  }\n}"}}}}}, "401": {"description": "401", "content": {"text/plain": {"examples": {"Result": {"value": "{\n  statusCode: 401,\n  error: 'Unauthorized Request',\n  message: 'x-api-key must be valid',\n  validation: {}\n}"}}}}}, "404": {"description": "404", "content": {"text/plain": {"examples": {"Result": {"value": "{\n  statusCode: 404,\n  error: 'Not Found',\n  message: '',\n  validation: { }\n}"}}}}}, "429": {"description": "429", "content": {"text/plain": {"examples": {"Result": {"value": "{\n  statusCode: 429,\n  error: 'Rate Limit Exceeded',\n  message: '',\n  validation: { }\n}"}}}}}, "500": {"description": "500", "content": {"text/plain": {"examples": {"Result": {"value": "{\n  statusCode: 500,\n  error: 'Server Error, Please Contact Support (<EMAIL>)',\n  message: '',\n  validation: { }\n}"}}}}}}, "deprecated": false}}, "/v2/PropertyDetailBulk": {"post": {"summary": "Property Detail Bulk API", "description": "For retrieving of up to 1000 properties at once.  Can be used standalone, but it's designed to work together with the Property Search API.  Use this API for quickly exporting lists, or bulk search requests for analytics.  Pass in addresses, or a list of ID's returned from the Property Search API.", "operationId": "property-detail-bulk-api", "parameters": [{"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "description": "List of property ids returned in the response of our Property Search API", "items": {"type": "integer", "format": "int32"}}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"examples": {"Result": {"value": "{\n  input: {\n    ids: [<<Your input property ids>>]\n  },\n  data: [\n     \t\"id\": 175203,\n      \"priorId\": 1814285802,\n      \"vacant\": true,\n      \"equity\": 194668,\n      \"ownerInfo\": {\n          \"ownerOccupied\": true,\n          \"absenteeOwner\": false,\n          \"instateAbsenteeOwner\": false,\n          \"outOfStateAbsenteeOwner\": false,\n          \"corporateOwned\": false,\n          \"ownershipLength\": 6,\n          \"owner1FirstName\": \"BRIENNA\",\n          \"owner1LastName\": \"BAILEY\",\n          \"owner1FullName\": \"BRIENNA SHOWALTER BAILEY\",\n          \"owner2FirstName\": \"NOEL\",\n          \"owner2LastName\": \"NOEL\",\n          \"owner2FullName\": \"NOEL\",\n          \"mailAddress\": {\n              \"fips\": \"41051\",\n              \"house\": \"3584\",\n              \"address\": \"3584 SE HOLGATE BLVD\",\n              \"street\": \"HOLGATE\",\n              \"preDirection\": \"SE\",\n              \"streetType\": \"BL<PERSON>\",\n              \"city\": \"PORTLAND\",\n              \"state\": \"OR\",\n              \"zip\": \"97202\",\n              \"zip4\": \"3359\",\n              \"carrierRoute\": \"C033\",\n              \"addressFormat\": \"S\",\n              \"label\": \"3584 SE HOLGATE BLVD, PORTLAND, OR 97202\"\n          },\n          \"equity\": 194668\n      },\n      \"propertyInfo\": {\n          \"address\": {\n              \"fips\": \"41051\",\n              \"house\": \"3584\",\n              \"address\": \"3584 SE HOLGATE BLVD\",\n              \"street\": \"HOLGATE\",\n              \"preDirection\": \"SE\",\n              \"streetType\": \"BLVD\",\n              \"city\": \"PORTLAND\",\n              \"state\": \"OR\",\n              \"zip\": \"97202\",\n              \"zip4\": \"3359\",\n              \"carrierRoute\": \"C033\",\n              \"congressionalDistrict\": \"329\",\n              \"label\": \"3584 SE HOLGATE BLVD, PORTLAND, OR 97202\"\n          },\n          \"propertyType\": \"SINGLE FAMILY RESIDENCE\",\n          \"latitude\": 45.49011300000000090903995442204177379608154296875,\n          \"longitude\": -122.627015000000000100044417195022106170654296875,\n          \"stories\": 2,\n          \"roomsCount\": 1,\n          \"bedrooms\": 3,\n          \"bathrooms\": 3,\n          \"partialBathrooms\": 0,\n          \"parkingSpaces\": 1,\n          \"lotSquareFeet\": 3392,\n          \"buildingSquareFeet\": 2953,\n          \"livingSquareFeet\": 2590,\n          \"yearBuilt\": 2017,\n          \"fireplace\": true,\n          \"fireplaces\": 1,\n          \"garageType\": \"11\",\n          \"garageSquareFeet\": 363,\n          \"basementType\": \"NO BASEMENT\",\n          \"basementSquareFeet\": 0,\n          \"basementSquareFeetFinished\": 0,\n          \"basementSquareFeetUnfinished\": \"0\",\n          \"heatingType\": \"FORCED AIR\",\n          \"heatingFuelType\": \"GAS\",\n          \"pool\": false,\n          \"garageSquareFeetFinished\": null,\n          \"garageSquareFeetUnfinished\": null,\n          \"basementSquareFeetUnFinished\": null,\n          \"basementFinishedPercent\": null\n      },\n      \"lotInfo\": {\n          \"legalDescription\": \"HEARN, BLOCK 2, LOT 2\",\n          \"subdivision\": \"HEARN\",\n          \"apn\": \"1S1E13AB 01700\",\n          \"apnUnformatted\": \"1S1E13AB01700\",\n          \"landUse\": \"Residential\",\n          \"propertyClass\": \"The general use for the property is for residential purposes\",\n          \"propertyType\": \"SINGLE FAMILY RESIDENCE\",\n          \"censusTract\": \"301\",\n          \"lotSquareFeet\": 3392,\n          \"lotAcres\": 0,\n          \"lotNumber\": \"2\",\n          \"zoning\": \"R5\"\n      },\n      \"taxInfo\": {\n          \"propertyId\": 175203,\n          \"year\": 2021,\n          \"taxAmount\": 863900,\n          \"assessmentYear\": 2021,\n          \"assessedValue\": 319520,\n          \"assessedImprovementValue\": \"0\",\n          \"marketValue\": 714400,\n          \"marketImprovementValue\": 487900,\n          \"marketLandValue\": 226500,\n          \"assessedLandValue\": 0\n      },\n      \"demographics\" : {\n\t\t\t\t\"medianIncome\" : \"130336\",\n \t\t\t\t\"suggestedRent\" : \"3010\",\n\t\t\t\t\"fmrYear\": \"2023\",\n\t\t\t\t\"fmrEfficiency\" : \"1750\",\n\t\t\t\t\"fmrOneBedroom\" : \"1780\",\n\t\t\t\t\"fmrTwoBedroom\" : \"2020\",\n\t\t\t\t\"fmrThreeBedroom\" : \"2530\",\n\t\t\t\t\"fmrFourBedroom\" : \"3010\",\n\t\t\t\t\"hudAreaName\" : \"Washington-Arlington-Alexandria, DC-VA-MD HUD Metro FMR Area\",\n  \t\t\t\"hudAreaCode\" : \"METRO47900M47900\"\n\t\t\t},\n      \"currentMortgages\": [\n          {\n              \"seq\": \"0\",\n              \"position\": \"First\",\n              \"recordingDate\": \"2021-10-21T00:00:00.000Z\",\n              \"amount\": 543932,\n              \"interestRate\": 0,\n              \"documentDate\": \"2021-10-21T00:00:00.000Z\",\n              \"lenderName\": \"CALIBER HOME LOANS INC\",\n              \"deedType\": \"CONVENTIONAL\"\n          },\n          {\n              \"seq\": \"1\",\n              \"position\": \"Second\",\n              \"amount\": 0,\n              \"interestRate\": 0,\n              \"lenderName\": \"\",\n              \"recordingDate\": null,\n              \"documentDate\": null\n          }\n      ],\n      \"mortgageHistory\": [\n          {\n              \"recordingDate\": \"2021-10-21T00:00:00.000Z\",\n              \"amount\": 543932,\n              \"interestRate\": 0,\n              \"documentDate\": \"2021-10-21T00:00:00.000Z\",\n              \"maturityDate\": \"2582409600000\",\n              \"lenderName\": \"CALIBER HOME LOANS INC\",\n              \"term\": \"361\",\n              \"termType\": \"MONTH\",\n              \"deedType\": \"WARRANTY DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"loanType\": \"CONVENTIONAL\",\n              \"granteeName\": \"BRIENNA SHOWALTER BAILEY, NOEL WHEAT KENDEL\",\n              \"seq\": 0\n          },\n          {\n              \"recordingDate\": \"2017-06-09T00:00:00.000Z\",\n              \"amount\": 389000,\n              \"interestRate\": 0,\n              \"documentDate\": \"2017-06-09T00:00:00.000Z\",\n              \"maturityDate\": \"2443564800000\",\n              \"lenderName\": \"FIRST FIN'L BK&TR\",\n              \"term\": \"360\",\n              \"interestRateType\": \"UNKNOWN\",\n              \"termType\": \"MONTH\",\n              \"deedType\": \"WARRANTY DEED\",\n              \"transactionType\": \"SUBDIVISION RELATED TRANSFER\",\n              \"loanType\": \"CONVENTIONAL\",\n              \"granteeName\": \"DOUGLAS C MCCORD, TRACEY A MCCORD\",\n              \"seq\": 1\n          },\n          {\n              \"recordingDate\": \"2008-08-05T00:00:00.000Z\",\n              \"amount\": 236000,\n              \"documentDate\": \"2008-08-05T00:00:00.000Z\",\n              \"lenderName\": \"H & R BLOCK BANK\",\n              \"interestRateType\": \"ADJUSTABLE RATE\",\n              \"deedType\": \"DEED OF TRUST\",\n              \"transactionType\": \"MORTGAGE\",\n              \"granteeName\": \"RODRICK D HORNER, JOANN M HORNER\",\n              \"seq\": 2,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2008-07-22T00:00:00.000Z\",\n              \"amount\": 236000,\n              \"documentDate\": \"2008-07-22T00:00:00.000Z\",\n              \"lenderName\": \"H & R BLOCK BANK\",\n              \"interestRateType\": \"ADJUSTABLE RATE\",\n              \"deedType\": \"DEED OF TRUST\",\n              \"transactionType\": \"MORTGAGE\",\n              \"granteeName\": \"RODRICK D HORNER, JOANN M HORNER\",\n              \"seq\": 3,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2007-01-31T00:00:00.000Z\",\n              \"amount\": 231300,\n              \"documentDate\": \"2007-01-31T00:00:00.000Z\",\n              \"lenderName\": \"OPTION ONE MORTGAGE CORP\",\n              \"interestRateType\": \"ADJUSTABLE RATE\",\n              \"deedType\": \"DEED OF TRUST\",\n              \"transactionType\": \"MORTGAGE\",\n              \"granteeName\": \"RODRICK D HORNER, JOANN M HORNER\",\n              \"seq\": 4,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2005-03-15T00:00:00.000Z\",\n              \"amount\": 165000,\n              \"documentDate\": \"2005-03-15T00:00:00.000Z\",\n              \"lenderName\": \"CHERI D COOLEY\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"CONSTRUCTION SALE\",\n              \"loanType\": \"BUILDING OR CONSTRUCTION\",\n              \"granteeName\": \"WYNDYM DEV LLC\",\n              \"seq\": 5,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2005-03-15T00:00:00.000Z\",\n              \"amount\": 165000,\n              \"documentDate\": \"2005-03-15T00:00:00.000Z\",\n              \"lenderName\": \"CHERI D COOLEY\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"CONSTRUCTION SALE\",\n              \"loanType\": \"BUILDING OR CONSTRUCTION\",\n              \"granteeName\": \"WYNDYM DEV LLC\",\n              \"seq\": 6,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2003-08-19T00:00:00.000Z\",\n              \"amount\": 269000,\n              \"documentDate\": \"2003-08-19T00:00:00.000Z\",\n              \"lenderName\": \"FIRST MAGNUS FINANCIAL CORP\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"PAUL GAGNON\",\n              \"seq\": 7,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2003-08-19T00:00:00.000Z\",\n              \"amount\": 269000,\n              \"documentDate\": \"2003-08-19T00:00:00.000Z\",\n              \"lenderName\": \"FIRST MAGNUS FINANCIAL CORP\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"PAUL GAGNON\",\n              \"seq\": 8,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2003-01-03T00:00:00.000Z\",\n              \"amount\": 129150,\n              \"documentDate\": \"2003-01-03T00:00:00.000Z\",\n              \"lenderName\": \"EAGLE HOME MORTGAGE CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"SUBDIVISION RELATED TRANSFER\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 9,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2003-01-03T00:00:00.000Z\",\n              \"amount\": 129150,\n              \"documentDate\": \"2003-01-03T00:00:00.000Z\",\n              \"lenderName\": \"EAGLE HOME MORTGAGE CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"SUBDIVISION RELATED TRANSFER\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 10,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2002-07-08T00:00:00.000Z\",\n              \"amount\": 84000,\n              \"documentDate\": \"2002-07-08T00:00:00.000Z\",\n              \"lenderName\": \"CLUNAS DEVELOPMENT CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"Unknown\",\n              \"transactionType\": \"TRANSFER\",\n              \"loanType\": \"CONVENTIONAL WITH PMI\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 11,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2002-07-08T00:00:00.000Z\",\n              \"amount\": 84000,\n              \"documentDate\": \"2002-07-08T00:00:00.000Z\",\n              \"lenderName\": \"CLUNAS DEVELOPMENT CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"Unknown\",\n              \"transactionType\": \"TRANSFER\",\n              \"loanType\": \"CONVENTIONAL WITH PMI\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 12,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2002-07-08T00:00:00.000Z\",\n              \"amount\": 84000,\n              \"documentDate\": \"2002-07-08T00:00:00.000Z\",\n              \"lenderName\": \"CLUNAS DEVELOPMENT CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"Unknown\",\n              \"transactionType\": \"TRANSFER\",\n              \"loanType\": \"CONVENTIONAL WITH PMI\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 13,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"2002-07-08T00:00:00.000Z\",\n              \"amount\": 84000,\n              \"documentDate\": \"2002-07-08T00:00:00.000Z\",\n              \"lenderName\": \"CLUNAS DEVELOPMENT CO\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"Unknown\",\n              \"transactionType\": \"TRANSFER\",\n              \"loanType\": \"CONVENTIONAL WITH PMI\",\n              \"granteeName\": \"CHARLES V HOWLETT\",\n              \"seq\": 14,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"1997-10-02T00:00:00.000Z\",\n              \"amount\": 92000,\n              \"documentDate\": \"1997-10-02T00:00:00.000Z\",\n              \"lenderName\": \"SELLER\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"PATRICK R BOONE\",\n              \"seq\": 15,\n              \"interestRate\": null\n          },\n          {\n              \"recordingDate\": \"1997-10-02T00:00:00.000Z\",\n              \"amount\": 92000,\n              \"documentDate\": \"1997-10-02T00:00:00.000Z\",\n              \"lenderName\": \"SELLER\",\n              \"interestRateType\": \"FIXED RATE\",\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"PATRICK R BOONE\",\n              \"seq\": 16,\n              \"interestRate\": null\n          },\n          {\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"SUBDIVISION RELATED TRANSFER\",\n              \"granteeName\": \"RODRICK D HORNER, JOANN M HORNER\",\n              \"seq\": 17,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"CRESENT CUSTOM HOMES INC\",\n              \"seq\": 18,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"FIRST MAGNUS FINANCIAL CORP\",\n              \"seq\": 19,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"GRANT DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"WYNDYM DEV LLC\",\n              \"seq\": 20,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"QUIT CLAIM DEED\",\n              \"transactionType\": \"TRANSFER\",\n              \"granteeName\": \"COUNTRYWIDE HOME LOANS INC\",\n              \"seq\": 21,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"TRUSTEES DEED\",\n              \"transactionType\": \"FORECLOSURE\",\n              \"granteeName\": \"FEDERAL HOME LOAN MTG CORP\",\n              \"seq\": 22,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          },\n          {\n              \"deedType\": \"DEED IN LIEU OF FORECLOSURE\",\n              \"transactionType\": \"FORECLOSURE\",\n              \"granteeName\": \"DLJ MTG CAP INC\",\n              \"seq\": 23,\n              \"amount\": null,\n              \"interestRate\": null,\n              \"recordingDate\": null,\n              \"documentDate\": null\n          }\n      ],\n      \"saleHistory\": [\n          {\n              \"recordingDate\": \"2021-10-21T00:00:00.000Z\",\n              \"saleDate\": \"2021-10-21T00:00:00.000Z\",\n              \"saleAmount\": 680000,\n              \"documentType\": \"DTWD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"BRIENNA SHOWALTER BAILEY, NOEL WHEAT KENDEL\",\n              \"sellerNames\": \"DOUGLAS C MCCORD, TRACEY A MCCORD\",\n              \"purchaseMethod\": \"Financed\",\n              \"downPayment\": 136068,\n              \"ltv\": 79.9899999999999948840923025272786617279052734375,\n              \"seq\": 0\n          },\n          {\n              \"recordingDate\": \"2017-06-09T00:00:00.000Z\",\n              \"saleDate\": \"2017-06-08T00:00:00.000Z\",\n              \"saleAmount\": 589000,\n              \"documentType\": \"DTWD\",\n              \"transactionType\": \"50\",\n              \"buyerNames\": \"DOUGLAS C MCCORD, TRACEY A MCCORD\",\n              \"sellerNames\": \"CRESCENT CUSTOM HOMES INC\",\n              \"purchaseMethod\": \"Financed\",\n              \"downPayment\": 200000,\n              \"ltv\": 66.0441000000000002501110429875552654266357421875,\n              \"seq\": 1\n          },\n          {\n              \"recordingDate\": \"2015-11-13T00:00:00.000Z\",\n              \"saleDate\": \"2015-11-06T00:00:00.000Z\",\n              \"saleAmount\": 200000,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"CRESENT CUSTOM HOMES INC\",\n              \"sellerNames\": \"DLJ MTG CAP INC\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 2\n          },\n          {\n              \"recordingDate\": \"2014-06-04T00:00:00.000Z\",\n              \"saleDate\": \"2014-02-20T00:00:00.000Z\",\n              \"saleAmount\": 0,\n              \"documentType\": \"DTDL\",\n              \"transactionType\": \"20\",\n              \"buyerNames\": \"DLJ MTG CAP INC\",\n              \"sellerNames\": \"HORNER,RODRICK D & JOANN M\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 3\n          },\n          {\n              \"recordingDate\": \"2004-08-09T00:00:00.000Z\",\n              \"saleDate\": \"2004-07-29T00:00:00.000Z\",\n              \"saleAmount\": 91350,\n              \"documentType\": \"DTTD\",\n              \"transactionType\": \"20\",\n              \"buyerNames\": \"FEDERAL HOME LOAN MTG CORP\",\n              \"sellerNames\": \"TRANSNATION TITLE\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 4\n          },\n          {\n              \"recordingDate\": \"2005-07-25T00:00:00.000Z\",\n              \"saleAmount\": 0,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"50\",\n              \"buyerNames\": \"RODRICK D HORNER, JOANN M HORNER\",\n              \"sellerNames\": \"WYNDYM DEV LLC\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 5,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2003-08-19T00:00:00.000Z\",\n              \"saleAmount\": 269000,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"PAUL GAGNON\",\n              \"sellerNames\": \"HOWLETT,CHARLES V\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 100,\n              \"seq\": 6,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2005-02-04T00:00:00.000Z\",\n              \"saleAmount\": 0,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"FIRST MAGNUS FINANCIAL CORP\",\n              \"sellerNames\": \"COUNTRYWIDE HOME LOANS\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 7,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2003-01-03T00:00:00.000Z\",\n              \"saleAmount\": 89000,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"50\",\n              \"buyerNames\": \"CHARLES V HOWLETT\",\n              \"sellerNames\": \"CLUNAS DEVELOPMENT CO\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 145.112400000000008049028110690414905548095703125,\n              \"seq\": 8,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2005-03-15T00:00:00.000Z\",\n              \"saleAmount\": 117896,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"10\",\n              \"buyerNames\": \"WYNDYM DEV LLC\",\n              \"sellerNames\": \"FIRST MAGNUS FINANCIAL CORP\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 139.953900000000004411049303598701953887939453125,\n              \"seq\": 9,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"1997-10-02T00:00:00.000Z\",\n              \"saleAmount\": 92000,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"PATRICK R BOONE\",\n              \"sellerNames\": \"COX,PRISCILLA J\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 100,\n              \"seq\": 10,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2005-07-25T00:00:00.000Z\",\n              \"saleAmount\": 0,\n              \"documentType\": \"DTGD\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"WYNDYM DEV LLC\",\n              \"sellerNames\": \"COOLEY,CHERI D\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 11,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2005-02-04T00:00:00.000Z\",\n              \"saleAmount\": 0,\n              \"documentType\": \"DTQC\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"COUNTRYWIDE HOME LOANS INC\",\n              \"sellerNames\": \"FEDERAL NATIONAL MORTGAGE ASSN\",\n              \"purchaseMethod\": \"Cash Purchase\",\n              \"downPayment\": 0,\n              \"ltv\": 0,\n              \"seq\": 12,\n              \"saleDate\": null\n          },\n          {\n              \"recordingDate\": \"2002-07-08T00:00:00.000Z\",\n              \"saleAmount\": 89000,\n              \"documentType\": \"XXXX\",\n              \"transactionType\": \"40\",\n              \"buyerNames\": \"CHARLES V HOWLETT\",\n              \"sellerNames\": \"CLUNAS DEVELOPMENT CO\",\n              \"purchaseMethod\": \"Financed\",\n              \"downPayment\": 5000,\n              \"ltv\": 94.38200000000000500222085975110530853271484375,\n              \"seq\": 13,\n              \"saleDate\": null\n          }\n      ],\n      \"foreclosureInfo\": []\n    },\n      {\n        //a new object for each additional \"id\" provided to the bulk API\n      }\n  ],\n  statusCode: 200,\n  statusMessage: 'Success',\n  credits: 10\n}"}}}}}}, "deprecated": false, "x-readme": {"code-samples": [{"language": "curl", "code": "curl --request POST \\\n  --url https://api.realestateapi.com/v2/PropertyDetailBulk \\\n  --header 'Content-Type: application/json' \\\n  --header 'x-api-key: <<your api key>> ' \\\n  --data '{\n  \"ids\": [718293, 920392, 811232]\n}'"}], "samples-languages": ["curl"]}}}, "/v2/PropertySearch": {"post": {"summary": "Property Search API", "description": "Searchable API for list building, search counts, and advanced filtering on properties.  You can also use this API to implement your own comparables API, or property analytics API.  Questions?  Contact our team to ask us for best practices with using this API.This API implements easy paging so your apps can easily manage filtered results in a results pane with paging.  When your user clicks on a result, just use the id from this API to get the full property results using the Property Detail API.  Questions on best practices for implementing paged property results in your app?  Just contact our team.", "operationId": "property-search-api", "parameters": [{"name": "x-api-key", "in": "header", "description": "User's API key", "required": true, "schema": {"type": "string"}}, {"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "boolean", "description": "Set to true to only return the count for the total  number of records that would be returned for the search and not the records themselves."}, "ids": {"type": "array", "description": "Provide a list of property IDs from past or saved Property Searches to pull back all of the enriched fields", "items": {"type": "integer", "format": "int32"}}, "ids_only": {"type": "boolean", "description": "Returns up to 10,000 property IDs matching your search criteria. When provided, the \"size\" and \"resultIndex\" will be ignored.", "default": false}, "obfuscate": {"type": "boolean", "description": "Will remove the address and name fields on the properties returned", "default": false}, "sort": {"type": "object", "description": "Sorts result set based on user defined sorting definitions across the Property Search fields"}, "summary": {"type": "boolean", "description": "Returns an aggregation of all lead types in a summary object. The summary object will return totals for each lead type within the context of the given search.", "default": false}, "resultIndex": {"type": "integer", "description": "Used with size to accomplish paging.  The server will skip the number of records specified by resultIndex, and return the records starting after the resultIndex.  The total number of records returned will not be greater than the size specified, or a max of 250 set by the server.", "format": "int32"}, "size": {"type": "integer", "description": "Set to the maximum number of records that the server can return for the search.  Used in conjunction with resultIndex for paging results.", "default": 50, "format": "int32"}, "address": {"type": "string", "description": "Fully formatted address for a property search.  This should include house, street, city, state and zip"}, "house": {"type": "string", "description": "Used to search for specific house numbers.   Must be accompanied with state or zip to limit results."}, "street": {"type": "string", "description": "Used to search searching street names only.  Must be accompanied with state or zip to limit results."}, "city": {"type": "string", "description": "Used to search within a city only.  Must be accompanied with state or zip to limit results."}, "state": {"type": "string", "description": "Used to search within a state.  Must be accompanied by city, house, or street to limit results."}, "county": {"type": "string", "description": "Used to search within a county.  Must be accompanied by state, or zip."}, "zip": {"type": "string", "description": "Used to search within a US zip code. An array of zips (of type:string) can also be provided to this field."}, "latitude": {"type": "number", "description": "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center", "format": "float"}, "longitude": {"type": "number", "description": "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center", "format": "float"}, "radius": {"type": "number", "description": "Provide a search radius between 0.1-10 miles for narrowing your search", "format": "float"}, "polygon": {"type": "array", "description": "Provide an array of latitude/longitude pairs for the Geo portion of your query", "items": {"properties": {"lat": {"type": "number", "format": "double"}, "lon": {"type": "number", "format": "double"}}, "type": "object"}}, "multi_polygon": {"type": "array", "description": "Minimum of 1 polygon", "items": {"properties": {"boundaries": {"type": "array", "items": {"properties": {"lat": {"type": "number", "format": "double"}, "lon": {"type": "number", "format": "double"}}, "type": "object"}}}, "type": "object"}}, "property_type": {"type": "string", "description": "Provide the type of residences/properties you are looking for", "enum": ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"]}, "property_use_code": {"type": "integer", "description": "Also accepts an Array of Integers, where each integer is one of our accepted Property Use Codes. See all codes here: https://developer.realestateapi.com/reference/property-use-codes-reference", "format": "int32"}, "mls_active": {"type": "boolean", "description": "Find active MLS listings"}, "mls_pending": {"type": "boolean", "description": "Find pending MLS sales that are expected to close"}, "mls_cancelled": {"type": "boolean", "description": "Find terminated MLS listings"}, "mls_sold": {"type": "boolean", "description": "Find sold MLS listings"}, "mls_days_on_market_min": {"type": "integer", "description": "Find properties that have been on the market for a certain amount of days. Use with \"mls_active\": true, \"mls_pending\": true or \"mls_cancelled\": true", "format": "int32"}, "mls_days_on_market_max": {"type": "integer", "format": "int32"}, "mls_listing_price_min": {"type": "integer", "description": "Lower bound used with mls_listing_max to only find properties with MLS listing prices within a defined range", "format": "int32"}, "mls_listing_price_max": {"type": "integer", "description": "Minimum value of 1", "format": "int32"}, "mls_listing_price": {"type": "integer", "description": "The official MLS listing price for the property", "format": "int32"}, "mls_listing_price_operator": {"type": "string", "description": "mls_operator is to be used with mls_listing_price to indicate a range less than or greater than starting with that listing price. For example, { mls_listing_price: 100000, mls_operator: 'gte' } would retrieve all properties with an MLS listing price of $100,000 or more", "enum": ["lt", "lte", "gt", "gte"]}, "id": {"type": "string", "description": "AutoComplete Field. Can be a string or an integer. Represents the unique property id in the case of full address autocomplete searches."}, "apn": {"type": "string", "description": "AutoComplete Field. The Property's unique tax assessor identifier, returned as part of the AutoComplete API response."}, "stateId": {"type": "string", "description": "AutoComplete Field."}, "countyId": {"type": "string", "description": "AutoComplete Field."}, "neighborhood_id": {"type": "integer", "description": "Autocomplete field.", "format": "int32"}, "neighborhood_name": {"type": "string", "description": "Autocomplete field."}, "searchType": {"type": "string", "description": "AutoComplete Field. A = full address ; C = city ; N = county; S = street ; Z = zip; G = neighborhood; T = state", "enum": ["A", "C", "N", "S", "Z", "G", "T"]}, "fips": {"type": "string", "description": "AutoComplete Field."}, "title": {"type": "string", "description": "AutoComplete Field."}, "absentee_owner": {"type": "boolean", "description": "Used for searching for properties where the owner is not currently a resident.  Generally signifies a tenant or non-owner occupied property."}, "adjustable_rate": {"type": "boolean", "description": "Indicates if the current mortgage on the property has an adjustable rate."}, "assumable": {"type": "boolean", "description": "Indicates if the mortgage on a given property is assumable."}, "attic": {"type": "boolean"}, "auction": {"type": "boolean", "description": "Used to find properties with an auction date.  Used with search_range or a default max of 1 year."}, "basement": {"type": "boolean", "description": "Used to find properties with a basement."}, "breezeway": {"type": "boolean"}, "carport": {"type": "boolean", "description": "Indicates properties with a carport structure."}, "cash_buyer": {"type": "boolean", "description": "Indicates if the property ownership is subsequent to an all cash transaction"}, "corporate_owned": {"type": "boolean", "description": "Used to find properties where one of the owners is company."}, "death": {"type": "boolean", "description": "Used to find properties where the property owner on the deed is recently deceased. Can be used for probate lists."}, "deck": {"type": "boolean", "description": "Used to find properties that have a deck"}, "feature_balcony": {"type": "boolean", "description": "Used to find properties with a balcony."}, "fire_sprinklers": {"type": "boolean", "description": "Used to find properties with registered fire sprinkler fixtures."}, "flood_zone": {"type": "boolean", "description": "Indicates if the property is in a flood zone area. This flag can be used in conjunction with \"flood_zone_type\" to get more specific result sets."}, "foreclosure": {"type": "boolean", "description": "Used to find properties in foreclosure.  Used with search_range or a default max of 1 year."}, "free_clear": {"type": "boolean", "description": "Used to find properties without an open mortgage."}, "garage": {"type": "boolean", "description": "Used to find properties with a physical structure marked for garage use."}, "high_equity": {"type": "boolean", "description": "Indicates properties with high equity (>39%)"}, "inherited": {"type": "boolean", "description": "Set to true to search inherited properties"}, "in_state_owner": {"type": "boolean", "description": "Used to find properties with an owner whose mailing address is in the same state as the property address."}, "investor_buyer": {"type": "boolean", "description": "Signals that the property was cash purchased by an absentee owner/investor, rather than individual like with the cash_buyer flag"}, "judgment": {"type": "boolean", "description": "Used to find properties where a lawsuit has been filed against a property owner or a party involved in a real estate transaction, and the court rules in favor of one of the parties, and issued a judgment."}, "mfh_2to4": {"type": "boolean", "description": "Multi-family homes with 2 to 4 units"}, "mfh_5plus": {"type": "boolean", "description": "Multi-family homes with 5 or more units"}, "out_of_state_owner": {"type": "boolean", "description": "Used to find properties with an owner whose mailing address is in a different state as the property address."}, "patio": {"type": "boolean", "description": "Used to find properties with a patio"}, "pool": {"type": "boolean", "description": "Used to find properties with a pool"}, "pre_foreclosure": {"type": "boolean", "description": "Used to find poperties that have received any notice of preforeclosure.   Used with search_range or a default max of 1 year."}, "prior_owner_individual": {"type": "boolean", "description": "Helps determine what properties are the result of a Flip. Use with \"prior_owner_months_owned_min\"/\"prior_owner_months_owned_max\""}, "private_lender": {"type": "boolean", "description": "Returns all properties that are currently financed by a private lender"}, "quit_claim": {"type": "boolean", "description": "Indicates if the property ownership was subsequent to a quit claim"}, "reo": {"type": "boolean", "description": "Used to find properties owned by a bank, trust, services entity, or tax entity.  Used with search_range or a default max of 1 year."}, "rv_parking": {"type": "boolean", "description": "The property is designated as having RV Parking"}, "tax_lien": {"type": "boolean", "description": "Find properties where there is a tax lien against the property"}, "trust_owned": {"type": "boolean", "description": "The property is owned by a Trust"}, "vacant": {"type": "boolean", "description": "Used to find properties that are vacant"}, "census_block": {"type": "string", "description": "Values 1000-5000"}, "census_block_group": {"type": "string", "description": "Values 0-10"}, "census_tract": {"type": "string", "description": "Official tract number from the U.S. Census Bureau"}, "construction": {"type": "string", "description": "Full list of construction types: https://developer.realestateapi.com/reference/construction-types"}, "document_type_code": {"type": "string", "description": "Used to find a specific document type for more granular searches other than the booleans provided.  This field can also be assigned an array of document type codes. Used in conjunction with search_range, or a maximum default value of 1 year."}, "flood_zone_type": {"type": "string", "description": "B, C, X (for moderate to low risk areas); A, AE, A1-30, AH, AO, AR, A99, V, VE, V1 - V30 (High Risk - Coastal Areas); D (Undetermined Risk Zone)"}, "loan_type_code_first": {"type": "string", "description": "Refer to the Loan Codes that are searchable://developer.realestateapi.com/reference/loan-type-codes"}, "loan_type_code_second": {"type": "string"}, "loan_type_code_third": {"type": "string"}, "notice_type": {"type": "string", "description": "Search by the Recording Date of the .foreclosureInfo data for the specified notice type", "enum": ["FOR", "NOD", "NOL", "NTS", "REO"]}, "parcel_account_number": {"type": "string", "description": "e.g. 05-00925.01"}, "search_range": {"type": "string", "description": "Used in conjunction for reo, auction, foreclosure, and preforeclosure searches to limit the search to only return records where the event happened within the provided range.  All ranges work from NOW back to the provided range.", "enum": ["1_MONTH", "2_MONTH", "3_MONTH", "6_MON<PERSON>", "1_YEAR"]}, "sewage": {"type": "string", "description": "Options: <PERSON>, Yes, Septic, None, <PERSON>"}, "water_source": {"type": "string", "description": "Full list of water source types you can filter by: https://developer.realestateapi.com/reference/water-source-searches"}, "estimated_equity": {"type": "integer", "description": "Used in conjunction with the equity_percent_operator to find properties where the estimated equity amount is greater than or less than the value provided.  Equity dollar amount is computed as the difference of the estimated value less any known open mortgages.", "format": "int32"}, "equity_operator": {"type": "string", "description": "Comparison operator for searches using estimated_equity.  Returns properties based on a greater than, or less than operation coupled with the value provided for estimated_equity which is based on total dollars of equity estimated from the estimated value and any known open mortgages.", "enum": ["lt", "lte", "gt", "gte"]}, "equity_percent": {"type": "integer", "description": "Used in conjunction with the equity_percent_operator to find properties where the equity percentage is greater than or less than the value provided.  Equity percentage is a based on the difference of the computed LTV.", "format": "int32"}, "equity_percent_operator": {"type": "string", "description": "Comparison operator for searches using equity_percent.  Returns properties based on a greater than, or less than operation coupled with the value provided for equity_percent which is based on the difference of the calculated LTV.", "enum": ["lt", "lte", "gt", "gte"]}, "last_sale_date": {"type": "string", "description": "Find properties based on the date of the last sale history transaction", "format": "date"}, "last_sale_date_operator": {"type": "string", "description": "Used in conjunction with \"last_sale_date\" to find properties that satisfy the range for when they were last sold in a transaction.", "enum": ["lt", "lte", "gt", "gte"]}, "median_income": {"type": "integer", "description": "Find properties based on the median income of the Areas that contain the properties", "format": "int32"}, "median_income_operator": {"type": "string", "description": "Used in conjunction with the \"median_income\" field in order to specify the range lower or higher you want to look at from the given median_income.", "enum": ["lt", "lte", "gt", "gte"]}, "years_owned": {"type": "integer", "description": "Number value of the years owned you are searching for. To be used with years_owned_operator", "format": "int32"}, "years_owned_operator": {"type": "string", "description": "Operator for less than and greater than searches on years_owned field", "enum": ["lt", "lte", "gt", "gte"]}, "assessed_improvement_value_min": {"type": "integer", "description": "Value range search against the county assessed improvement value", "format": "int32"}, "assessed_improvement_value_max": {"type": "integer", "format": "int32"}, "assessed_land_value_min": {"type": "integer", "description": "Value range search against the county assessed land value", "format": "int32"}, "assessed_land_value_max": {"type": "integer", "format": "int32"}, "assessed_value_min": {"type": "integer", "description": "Value range search against the county assessed value", "format": "int32"}, "assessed_value_max": {"type": "integer", "format": "int32"}, "auction_date_min": {"type": "string", "description": "filter on dates of upcoming foreclosure auctions (e.g. Current Date \"2024-05-01\" & set a future date range of \"2024-05-15\" to \"2024-05-30\"). Use with \"auction\": true", "format": "date"}, "auction_date_max": {"type": "string", "format": "date"}, "baths_min": {"type": "integer", "description": "Used for searching a range of properties with bathrooms between a min and max.  Minimum numbers of bathrooms for the given property search", "format": "int32"}, "baths_max": {"type": "integer", "description": "Used for searching a range of properties with bathrooms between a min and max.  Maximum numbers of bathrooms for the given property search", "format": "int32"}, "beds_min": {"type": "integer", "description": "Used for searching a range of properties with bedrooms between a min and max.  Minimum numbers of bedrooms for the given property search", "format": "int32"}, "beds_max": {"type": "integer", "description": "Used for searching a range of properties with bedrooms between a min and max.  Maximum numbers of bedrooms for the given property search", "format": "int32"}, "building_size_min": {"type": "integer", "description": "Used for searching a range of properties with an interior, living square footage between a min and max.  Minimum square footage of the interior living space for the given property search", "format": "int32"}, "building_size_max": {"type": "integer", "description": "Used for searching a range of properties with an interior, living square footage between a min and max.  Maximum square footage of the interior living space for the given property search", "format": "int32"}, "deck_area_min": {"type": "integer", "description": "In sq. ft.", "format": "int32"}, "deck_area_max": {"type": "string", "description": "In sq. ft."}, "estimated_equity_min": {"type": "integer", "description": "Filter for properties based on the nominal value of equity owners have in their homes. Works well with \"value_min\"/\"value_max\".", "format": "int32"}, "estimated_equity_max": {"type": "integer", "format": "int32"}, "foreclosure_date_min": {"type": "string", "description": "Filter for properties based on a date range for when a specific Foreclosure document was recorded - use with \"foreclosure\": true & \"notice_type\"", "format": "date"}, "foreclosure_date_max": {"type": "string", "format": "date"}, "last_sale_date_min": {"type": "string", "description": "Minimum Date for the last sale transaction date", "format": "date"}, "last_sale_date_max": {"type": "string", "description": "Maximum Date for the last sale transaction date", "format": "date"}, "last_sale_price_min": {"type": "integer", "description": "Filter for properties based on a Last Sale Price range.", "format": "int32"}, "last_sale_price_max": {"type": "integer", "format": "int32"}, "lot_size_min": {"type": "integer", "description": "Used for searching a range of properties with lot sizes between a min and max.  Minimum square footage of the exterior lot  built for the given property search", "format": "int32"}, "lot_size_max": {"type": "integer", "description": "Used for searching a range of properties with lot sizes between a min and max.  Maximum square footage of the exterior lot  built for the given property search", "format": "int32"}, "ltv_min": {"type": "integer", "description": "Min. of 0", "format": "int32"}, "ltv_max": {"type": "string", "description": "Max of 100"}, "median_income_min": {"type": "integer", "description": "Filter for properties that are within a certain range of median income (Zipcode-level)", "format": "int32"}, "median_income_max": {"type": "integer", "format": "int32"}, "mortgage_min": {"type": "integer", "description": "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Minimum estimated amount for all open mortgages for the given property search.", "format": "int32"}, "mortgage_max": {"type": "integer", "description": "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Maximum estimated amount for all open mortgages for the given property search.", "format": "int32"}, "rooms_min": {"type": "integer", "description": "Used for setting the minimum on the number of total rooms you want your properties to have.", "format": "int32"}, "rooms_max": {"type": "integer", "description": "Used for setting the maximum on the number of total rooms you want your properties to have.", "format": "int32"}, "pool_area_min": {"type": "integer", "description": "In sq. ft.", "format": "int32"}, "pool_area_max": {"type": "integer", "description": "In sq. ft.", "format": "int32"}, "portfolio_equity_min": {"type": "integer", "description": "Used to find properties where the minimum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.", "format": "int32"}, "portfolio_equity_max": {"type": "integer", "description": "Used to find properties where the maximum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.", "format": "int32"}, "portfolio_mortgage_balance_min": {"type": "integer", "description": "Filter for properties based on the remaining open mortgage balance of the Portfolio for Owners with > 1 property", "format": "int32"}, "portfolio_mortgage_balance_max": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_min": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_max": {"type": "integer", "format": "int32"}, "portfolio_purchased_last6_min": {"type": "integer", "format": "int32"}, "portfolio_purchased_last6_max": {"type": "integer", "format": "int32"}, "portfolio_value_min": {"type": "integer", "description": "Filter for properties based on the Total Value of the Portfolio for Owners with > 1 property", "format": "int32"}, "portfolio_value_max": {"type": "integer", "format": "int32"}, "pre_foreclosure_date_min": {"type": "string", "description": "Filter by the Recording Date of Pre-Foreclosure Related Documents. Use with \"pre_foreclosure\": true", "format": "date"}, "pre_foreclosure_date_max": {"type": "string", "format": "date"}, "prior_owner_months_owned_min": {"type": "integer", "description": "Define the time range for what constitutes a \"Flip\" period between the last 2 transactions", "format": "int32"}, "prior_owner_months_owned_max": {"type": "integer", "format": "int32"}, "properties_owned_min": {"type": "integer", "description": "The minimum amount of total properties that any property owner's portfolio will have for each property returned.", "format": "int32"}, "properties_owned_max": {"type": "integer", "description": "The maximum amount of total properties that any property owner's portfolio will have for each property returned.", "format": "int32"}, "stories_min": {"type": "integer", "description": "The minimum amount of floors/stories you want properties in your response to have", "format": "int32"}, "stories_max": {"type": "integer", "description": "The maximum amount of floors/stories you want properties in your response to have", "format": "int32"}, "tax_delinquent_year_min": {"type": "integer", "description": "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.", "format": "int32"}, "tax_delinquent_year_max": {"type": "integer", "description": "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.", "format": "int32"}, "units_min": {"type": "integer", "description": "The minimum amount of individual units that the property contains", "format": "int32"}, "units_max": {"type": "integer", "description": "The maximum amount of individual units that the property contains", "format": "int32"}, "value_min": {"type": "integer", "description": "Used for searching a range of properties with an estimated value between a min and max.  Minimum estimated value for the given property search", "format": "int32"}, "value_max": {"type": "integer", "description": "Used for searching a range of properties with an estimated value between a min and max.  Maximum estimated value for the given property search.", "format": "int32"}, "year_min": {"type": "integer", "description": "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Minimum year built for the given property search", "format": "int32"}, "year_max": {"type": "integer", "description": "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Maximum year built for the given property search", "format": "int32"}, "year_built_min": {"type": "integer", "description": "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search", "format": "int32"}, "year_built_max": {"type": "integer", "description": "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search", "format": "int32"}, "years_owned_min": {"type": "integer", "description": "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_max", "format": "int32"}, "years_owned_max": {"type": "integer", "description": "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_min.", "format": "int32"}, "last_update_date_min": {"type": "string", "description": "fetch property IDs of properties that have been updated in a given time range.", "format": "date"}, "last_update_date_max": {"type": "string", "format": "date"}, "hoa": {"type": "boolean"}, "usps_mail_state": {"type": "string"}, "last_sale_arms_length": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"oneOf": [{"title": "Build a List of Properties", "type": "object", "properties": {"live": {"type": "boolean", "example": true, "default": true}, "input": {"type": "object", "properties": {"count": {"type": "boolean", "example": false, "default": true}, "mls_active": {"type": "boolean", "example": true, "default": true}, "city": {"type": "string", "example": "Herndon"}, "state": {"type": "string", "example": "VA"}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"absenteeOwner": {"type": "boolean", "example": false, "default": true}, "address": {"type": "object", "properties": {"address": {"type": "string", "example": "13723 <PERSON> Ave Unit 308, Herndon, Va 20171"}, "city": {"type": "string", "example": "Herndon"}, "county": {"type": "string", "example": "Fairfax"}, "state": {"type": "string", "example": "VA"}, "street": {"type": "string", "example": "13723 <PERSON> Ave Unit 308"}, "zip": {"type": "string", "example": "20171"}}}, "adjustableRate": {"type": "boolean", "example": false, "default": true}, "airConditioningAvailable": {"type": "boolean", "example": false, "default": true}, "assessedImprovementValue": {"type": "integer", "example": 326590, "default": 0}, "assessedLandValue": {"type": "integer", "example": 82000, "default": 0}, "assessedValue": {"type": "integer", "example": 408590, "default": 0}, "auction": {"type": "boolean", "example": false, "default": true}, "basement": {"type": "boolean", "example": false, "default": true}, "bathrooms": {"type": "integer", "example": 2, "default": 0}, "bedrooms": {"type": "integer", "example": 2, "default": 0}, "cashBuyer": {"type": "boolean", "example": false, "default": true}, "corporateOwned": {"type": "boolean", "example": false, "default": true}, "death": {"type": "boolean", "example": false, "default": true}, "distressed": {"type": "boolean", "example": false, "default": true}, "documentType": {"type": "string", "example": "<PERSON>"}, "documentTypeCode": {"type": "string", "example": "DTGD"}, "equity": {"type": "boolean", "example": false, "default": true}, "equityPercent": {"type": "integer", "example": 38, "default": 0}, "estimatedEquity": {"type": "integer", "example": 189084, "default": 0}, "estimatedValue": {"type": "integer", "example": 490308, "default": 0}, "floodZone": {"type": "boolean", "example": true, "default": true}, "floodZoneDescription": {"type": "string", "example": "AREA OF MINIMAL FLOOD HAZARD"}, "floodZoneType": {"type": "string", "example": "X"}, "foreclosure": {"type": "boolean", "example": false, "default": true}, "forSale": {"type": "boolean", "example": false, "default": true}, "freeClear": {"type": "boolean", "example": false, "default": true}, "garage": {"type": "boolean", "example": false, "default": true}, "highEquity": {"type": "boolean", "example": false, "default": true}, "id": {"type": "string", "example": "253175355"}, "inherited": {"type": "boolean", "example": false, "default": true}, "inStateAbsenteeOwner": {"type": "boolean", "example": false, "default": true}, "investorBuyer": {"type": "boolean", "example": false, "default": true}, "landUse": {"type": "string", "example": "Residential"}, "lastMortgage1Amount": {}, "lastSaleAmount": {"type": "string", "example": "418000"}, "lastSaleDate": {"type": "string", "example": "2020-07-23"}, "latitude": {"type": "number", "example": 38.920743, "default": 0}, "lenderName": {"type": "string", "example": "One American Mtg"}, "listingAmount": {}, "longitude": {"type": "number", "example": -77.421772, "default": 0}, "lotSquareFeet": {"type": "integer", "example": 0, "default": 0}, "mailAddress": {"type": "object", "properties": {"address": {"type": "string", "example": "13723 <PERSON> Ave Unit 308, Herndon, Va 20171"}, "city": {"type": "string", "example": "Herndon"}, "county": {"type": "string", "example": "Fairfax"}, "state": {"type": "string", "example": "VA"}, "street": {"type": "string", "example": "13723 <PERSON> Ave Unit 308"}, "zip": {"type": "string", "example": "20171"}}}, "medianIncome": {"type": "string", "example": "150066"}, "MFH2to4": {"type": "boolean", "example": false, "default": true}, "MFH5plus": {"type": "boolean", "example": false, "default": true}, "mlsActive": {"type": "boolean", "example": true, "default": true}, "mlsCancelled": {"type": "boolean", "example": false, "default": true}, "mlsDaysOnMarket": {"type": "integer", "example": 101, "default": 0}, "mlsFailed": {"type": "boolean", "example": false, "default": true}, "mlsHasPhotos": {"type": "boolean", "example": false, "default": true}, "mlsLastSaleDate": {"type": "string", "example": "2020-07-23"}, "mlsLastStatusDate": {"type": "string", "example": "2023-04-15"}, "mlsListingDate": {"type": "string", "example": "2023-04-15"}, "mlsListingPrice": {"type": "integer", "example": 2500, "default": 0}, "mlsPending": {"type": "boolean", "example": false, "default": true}, "mlsSold": {"type": "boolean", "example": false, "default": true}, "mlsStatus": {"type": "string", "example": "Active"}, "mlsType": {"type": "string", "example": "ForSale"}, "negativeEquity": {"type": "boolean", "example": false, "default": true}, "neighborhood": {"type": "object", "properties": {"center": {"type": "string", "example": "POINT(-77.421701188095 38.919564722822)"}, "id": {"type": "string", "example": "63205"}, "name": {"type": "string", "example": "Discovery Square"}, "type": {"type": "string", "example": "subdivision"}}}, "openMortgageBalance": {"type": "integer", "example": 313500, "default": 0}, "outOfStateAbsenteeOwner": {"type": "boolean", "example": false, "default": true}, "owner1FirstName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "owner1LastName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "owner2FirstName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "owner2LastName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "ownerOccupied": {"type": "boolean", "example": true, "default": true}, "preForeclosure": {"type": "boolean", "example": false, "default": true}, "privateLender": {"type": "boolean", "example": false, "default": true}, "propertyId": {"type": "string", "example": "253175355"}, "propertyType": {"type": "string", "example": "CONDO"}, "propertyUse": {"type": "string", "example": "Condominium"}, "propertyUseCode": {"type": "integer", "example": 366, "default": 0}, "rentAmount": {}, "reo": {"type": "boolean", "example": false, "default": true}, "roomsCount": {"type": "integer", "example": 5, "default": 0}, "squareFeet": {"type": "integer", "example": 1323, "default": 0}, "suggestedRent": {"type": "string", "example": "2070"}, "unitsCount": {"type": "integer", "example": 0, "default": 0}, "vacant": {"type": "boolean", "example": false, "default": true}, "yearBuilt": {"type": "integer", "example": 2015, "default": 0}, "yearsOwned": {"type": "integer", "example": 3, "default": 0}}}}, "resultCount": {"type": "integer", "example": 156, "default": 0}, "resultIndex": {"type": "integer", "example": 50, "default": 0}, "recordCount": {"type": "integer", "example": 50, "default": 0}, "statusCode": {"type": "integer", "example": 200, "default": 0}, "statusMessage": {"type": "string", "example": "success"}, "requestExecutionTimeMS": {"type": "string", "example": "126ms"}}}, {"title": "Property Count Example", "type": "object", "properties": {"input": {"type": "object", "properties": {"count": {"type": "boolean", "example": true, "default": true}, "zip": {"type": "string", "example": "08046"}, "beds_min": {"type": "integer", "example": 2, "default": 0}, "beds_max": {"type": "integer", "example": 4, "default": 0}, "baths_min": {"type": "integer", "example": 2, "default": 0}, "baths_max": {"type": "integer", "example": 5, "default": 0}, "equity": {"type": "integer", "example": 250000, "default": 0}, "equity_comparison": {"type": "string", "example": "lt"}}}, "data": {"type": "array", "items": {"type": "object"}}}}, {"title": "Obfuscate Properties Example", "type": "object", "properties": {"input": {"type": "object", "properties": {"count": {"type": "boolean", "example": false, "default": true}, "obfuscate": {"type": "boolean", "example": true, "default": true}, "state": {"type": "string", "example": "VA"}, "city": {"type": "string", "example": "Arlington"}}}, "data": {"type": "array", "items": {"type": "object"}}}}, {"title": "Property Summary", "type": "object", "properties": {"input": {"type": "object", "properties": {"summary": {"type": "boolean", "example": true, "default": true}, "zip": {"type": "string", "example": "08046"}, "beds_min": {"type": "integer", "example": 2, "default": 0}, "beds_max": {"type": "integer", "example": 4, "default": 0}, "baths_min": {"type": "integer", "example": 2, "default": 0}, "baths_max": {"type": "integer", "example": 5, "default": 0}, "equity": {"type": "integer", "example": 250000, "default": 0}, "equity_comparison": {"type": "string", "example": "lt"}}}, "data": {"type": "array"}}}]}}}}}, "deprecated": false}}, "/v2/PropertyComps": {"post": {"summary": "v2/PropertyComps API", "description": "", "operationId": "property-comps-api", "parameters": [{"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "Property ID of an object returned by our Property Search API", "format": "int32"}, "address": {"type": "string", "description": "A fully formatted address with the following form: 123 Main St, City State Zip"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"examples": {"Result": {"value": "{  \n\tlive: true,\n  input: { address: '<fully formatted address>' },\n  comps: [\n    {\n      id: '41794029',\n      priorId: '1842600223',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '41794029',\n      bedrooms: '4',\n      bathrooms: '3',\n      yearBuilt: '1951',\n      squareFeet: '1780',\n      estimatedValue: '1025381',\n      equityPercent: '24',\n      lastSaleDate: '2022-05-11',\n      lastSaleAmount: '1300000',\n      latitude: '38.889334000',\n      longitude: '-77.154853000',\n      openMortgageBalance: '780000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Owner1Name',\n      owner1LastName: 'May',\n      owner2FirstName: 'Stephanie',\n      owner2LastName: 'May',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Guaranteed Rate Inc',\n      address: [Object],\n      mailAddress: [Object],\n      medianIncome : \"130336\",\n      suggestedRent : \"3010\",\n      lotAcres: \"2\",\n      neighborhood: [Object]\n    },\n    {\n      id: '21916427',\n      priorId: '1842585399',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '21916427',\n      bedrooms: '4',\n      bathrooms: '3',\n      yearBuilt: '1928',\n      squareFeet: '2497',\n      estimatedValue: '1328142',\n      equityPercent: '27',\n      lastSaleDate: '2022-05-16',\n      lastSaleAmount: '1275000',\n      latitude: '38.*********',\n      longitude: '-77.*********',\n      openMortgageBalance: '970000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'James',\n      owner1LastName: 'Mcclellan',\n      owner2FirstName: 'Donna',\n      owner2LastName: 'Mcclellan',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Townebank Mortgage',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '*********',\n      priorId: '**********',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '*********',\n      bedrooms: '2',\n      bathrooms: '2',\n      yearBuilt: '1947',\n      squareFeet: '1820',\n      estimatedValue: '962992',\n      equityPercent: '11',\n      lastSaleDate: '2022-05-03',\n      lastSaleAmount: '1066000',\n      latitude: '38.*********',\n      longitude: '-77.*********',\n      openMortgageBalance: '852800',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Andrew',\n      owner1LastName: 'Dinh',\n      owner2FirstName: 'Meghan',\n      owner2LastName: 'Dinh',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Mclean Mortgage Corp',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '198867878',\n      priorId: '1842581429',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '198867878',\n      bedrooms: '4',\n      bathrooms: '5',\n      yearBuilt: '1938',\n      squareFeet: '2299',\n      estimatedValue: '1225737',\n      equityPercent: '-6',\n      lastSaleDate: '2022-05-13',\n      lastSaleAmount: '1910000',\n      latitude: '38.886680000',\n      longitude: '-77.154186000',\n      openMortgageBalance: '1295000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Jessica',\n      owner1LastName: 'Conner',\n      owner2FirstName: 'Dylan',\n      owner2LastName: 'Conner',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'George Mason Mortgage Corp',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '194066389',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '194066389',\n      bedrooms: '4',\n      bathrooms: '4',\n      yearBuilt: '1941',\n      squareFeet: '2344',\n      estimatedValue: '436080',\n      equityPercent: '-199',\n      lastSaleDate: '2022-05-06',\n      lastSaleAmount: '1500000',\n      latitude: '38.880667000',\n      longitude: '-77.156035000',\n      openMortgageBalance: '1305000',\n      landUse: 'Residential',\n      propertyType: 'Single Family Residence',\n      owner1FirstName: 'Marnie',\n      owner1LastName: 'Riddle',\n      owner2FirstName: 'Thomas',\n      owner2LastName: 'Colvin',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Atlantic Coast Mortgage',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '195693547',\n      priorId: '1842600101',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '195693547',\n      bedrooms: '5',\n      bathrooms: '3',\n      yearBuilt: '1955',\n      squareFeet: '2594',\n      estimatedValue: '1115653',\n      equityPercent: '16',\n      lastSaleDate: '2022-05-17',\n      lastSaleAmount: '1150000',\n      latitude: '38.890646000',\n      longitude: '-77.154763000',\n      openMortgageBalance: '938400',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Michael',\n      owner1LastName: 'Mullen',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Home Express Mortgage Corp',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '198675958',\n      priorId: '1842602394',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '198675958',\n      bedrooms: '3',\n      bathrooms: '2',\n      yearBuilt: '1956',\n      squareFeet: '1175',\n      estimatedValue: '842571',\n      equityPercent: '-66',\n      lastSaleDate: '2022-04-07',\n      lastSaleAmount: '1750000',\n      latitude: '38.878930000',\n      longitude: '-77.150310000',\n      openMortgageBalance: '1400000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Stanley',\n      owner1LastName: 'Smith',\n      owner2FirstName: 'Mirna',\n      owner2LastName: 'Smith',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'First Savings Mortgage Corp',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '20868226',\n      priorId: '1842600002',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '20868226',\n      bedrooms: '3',\n      bathrooms: '3',\n      yearBuilt: '1954',\n      squareFeet: '1113',\n      estimatedValue: '898470',\n      equityPercent: '-8',\n      lastSaleDate: '2022-05-06',\n      lastSaleAmount: '1087000',\n      latitude: '38.*********',\n      longitude: '-77.*********',\n      openMortgageBalance: '970000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Allyce',\n      owner1LastName: 'Jordan',\n      owner2FirstName: 'Carl',\n      owner2LastName: 'Leighty',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Freedom Bank',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '********',\n      priorId: '**********',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '********',\n      bedrooms: '2',\n      bathrooms: '2',\n      yearBuilt: '1941',\n      squareFeet: '1125',\n      estimatedValue: '834342',\n      equityPercent: '5',\n      lastSaleDate: '2022-04-07',\n      lastSaleAmount: '995500',\n      latitude: '38.*********',\n      longitude: '-77.*********',\n      openMortgageBalance: '796400',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Burgdorf',\n      owner1LastName: 'Dorney',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Atlantic Coast Mortgage',\n      address: [Object],\n      mailAddress: [Object]\n    },\n    {\n      id: '********',\n      priorId: '1842615798',\n      vacant: false,\n      absenteeOwner: false,\n      corporateOwned: false,\n      outOfStateAbsenteeOwner: false,\n      inStateAbsenteeOwner: false,\n      propertyId: '********',\n      bedrooms: '5',\n      bathrooms: '4',\n      yearBuilt: '2019',\n      squareFeet: '2688',\n      estimatedValue: '1305076',\n      equityPercent: '-9',\n      lastSaleDate: '2022-04-25',\n      lastSaleAmount: '1775000',\n      latitude: '38.882993000',\n      longitude: '-77.152075000',\n      openMortgageBalance: '1420000',\n      landUse: 'Residential',\n      propertyType: 'SFR',\n      owner1FirstName: 'Matthew',\n      owner1LastName: 'Kozey',\n      owner2FirstName: 'Elizabeth',\n      owner2LastName: 'Kozey',\n      preForeclosure: false,\n      cashBuyer: false,\n      privateLender: false,\n      lenderName: 'Draper & Kramer Mortgage Corp',\n      address: [Object],\n      mailAddress: [Object]\n    }\n  ],\n  reapiAvm: 1368707,\n  recordCount: 10,\n  statusCode: 200,\n  statusMessage: 'success',\n  credits: 0.25,\n  requestExecutionTimeMS: '435ms'\n}"}}}}}}, "deprecated": false, "x-readme": {"code-samples": [{"language": "curl", "code": "curl --request POST \\\n     --url https://api.realestateapi.com/v2/PropertyComps \\\n     --header 'Accept: application/json' \\\n     --header 'Content-Type: application/json'\n     --data `{\n       \"address\": \"123 Main St, Arlington VA 22205\"\n     }'"}], "samples-languages": ["curl"]}}}, "/v2/AddressVerification": {"post": {"summary": "Address Verification API", "description": "Verify 1 - 100 addresses at a time.", "operationId": "address-verification-api", "parameters": [{"name": "x-user-id", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"strict": {"type": "boolean"}, "addresses": {"type": "array", "description": "Provide up to 100 addresses per request", "items": {"properties": {"key": {"type": "string", "description": "This key will be returned in the response per address submitted to help relate the request to the response data."}, "address": {"type": "string", "description": "Formatted address including street, city, state and zip."}, "street": {"type": "string", "description": "Contains the house number, pre-direction, street name, street type and post-direction."}, "city": {"type": "string", "description": "Name of the city where the address is located."}, "state": {"type": "string", "description": "Two letter abbreviation for the state where the address is located."}, "zip": {"type": "string", "description": "Five digit zip code where the property is located."}, "fips": {"type": "string"}, "apn": {"type": "string"}}, "type": "object"}}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"oneOf": [{"title": "Using Full Address", "type": "object", "properties": {"input": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "integer", "example": 1, "default": 0}, "street": {"type": "string", "example": "2505 NW 28th Street"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "73107"}}}}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"input": {"type": "object", "properties": {"key": {"type": "integer", "example": 1, "default": 0}, "address": {"type": "string", "example": "2505 NW 28th St"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "73107"}}}, "id": {"type": "integer", "example": 24334491, "default": 0}, "address": {"type": "object", "properties": {"zip": {"type": "string", "example": "73107"}, "streetType": {"type": "string", "example": "St"}, "address": {"type": "string", "example": "2505 Nw 28th St"}, "city": {"type": "string", "example": "Oklahoma City"}, "preDirection": {"type": "string", "example": "Nw"}, "congressionalDistrict": {"type": "string", "example": "326"}, "county": {"type": "string", "example": "Oklahoma"}, "carrierRoute": {"type": "string", "example": "C022"}, "fips": {"type": "string", "example": "40109"}, "zip4": {"type": "string", "example": "2127"}, "label": {"type": "string", "example": "2505 Nw 28th St, Oklahoma City, Ok 73107"}, "house": {"type": "string", "example": "2505"}, "street": {"type": "string", "example": "28th"}, "state": {"type": "string", "example": "OK"}}}, "mailAddress": {"type": "object", "properties": {"zip": {"type": "string", "example": "73008"}, "streetType": {"type": "string", "example": "Ave"}, "address": {"type": "string", "example": "4508 N Peniel Ave"}, "city": {"type": "string", "example": "Bethany"}, "preDirection": {"type": "string", "example": "N"}, "addressFormat": {"type": "string", "example": "S"}, "county": {"type": "string", "example": "Oklahoma"}, "carrierRoute": {"type": "string", "example": "C003"}, "fips": {"type": "string", "example": "40109"}, "zip4": {"type": "string", "example": "2748"}, "label": {"type": "string", "example": "4508 N Peniel Ave, Bethany, Ok 73008"}, "house": {"type": "string", "example": "4508"}, "street": {"type": "string", "example": "<PERSON><PERSON>"}, "state": {"type": "string", "example": "OK"}}}, "vacant": {"type": "boolean", "example": false, "default": true}, "absenteeOwner": {"type": "boolean", "example": true, "default": true}, "apn": {"type": "string", "example": "06-790-4400"}, "latitude": {"type": "string", "example": "35.497951000"}, "longitude": {"type": "string", "example": "-97.556967000"}, "lotNumber": {"type": "string", "example": "45,46"}, "propertyUse": {"type": "string", "example": "Single Family Residence"}, "precision": {"type": "string", "example": "Zip9"}, "searchType": {"type": "string", "example": "A"}, "match": {"type": "boolean", "example": true, "default": true}}}}, "statusCode": {"type": "integer", "example": 200, "default": 0}, "statusMessage": {"type": "string", "example": "Success"}, "credits": {"type": "integer", "example": 0, "default": 0}, "live": {"type": "boolean", "example": true, "default": true}, "requestExecutionTimeMS": {"type": "string", "example": "325ms"}, "addressVerificationExecutionTimeMS": {"type": "string", "example": "323ms"}}}, {"title": "Address Not Found", "type": "object", "properties": {"input": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "integer", "example": 0, "default": 0}, "street": {"type": "string", "example": "2505 NW 28th Street"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "7310"}}}}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"input": {"type": "object", "properties": {"key": {"type": "integer", "example": 0, "default": 0}, "street": {"type": "string", "example": "2505 NW 28th Street"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "7310"}}}, "match": {"type": "boolean", "example": false, "default": true}, "error": {"type": "boolean", "example": true, "default": true}, "errorMessage": {"type": "string", "example": "Zip code must be 5 digits in length."}}}}, "statusCode": {"type": "integer", "example": 200, "default": 0}, "statusMessage": {"type": "string", "example": "Success"}, "credits": {"type": "integer", "example": 0, "default": 0}, "live": {"type": "boolean", "example": true, "default": true}, "requestExecutionTimeMS": {"type": "string", "example": "0ms"}, "addressVerificationExecutionTimeMS": {}}}]}}}}, "400": {"description": "400", "content": {"application/json": {"schema": {"type": "object", "properties": {"input": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "integer", "example": 0, "default": 0}, "street": {"type": "string", "example": "2505 NW 28th Street"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "7310"}}}}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"input": {"type": "object", "properties": {"key": {"type": "integer", "example": 0, "default": 0}, "street": {"type": "string", "example": "2505 NW 28th Street"}, "city": {"type": "string", "example": "Oklahoma City"}, "state": {"type": "string", "example": "OK"}, "zip": {"type": "string", "example": "7310"}}}, "match": {"type": "boolean", "example": false, "default": true}, "error": {"type": "boolean", "example": true, "default": true}, "errorMessage": {"type": "string", "example": "Zip code must be 5 digits in length."}}}}, "statusCode": {"type": "integer", "example": 200, "default": 0}, "statusMessage": {"type": "string", "example": "Success"}, "credits": {"type": "integer", "example": 0, "default": 0}, "live": {"type": "boolean", "example": true, "default": true}, "requestExecutionTimeMS": {"type": "string", "example": "20ms"}, "addressVerificationExecutionTimeMS": {}}}}}}}, "deprecated": false}}, "/v3/PropertyComps": {"post": {"summary": "/v3/PropertyComps API", "description": "Customize your comps model", "operationId": "v3-comps-response-object", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Property ID for subject property"}, "address": {"type": "string", "description": "The fully formatted address for your subject property (e.g. 123 Main St, Arlington, VA 22205)"}, "exact_match": {"type": "boolean", "description": "Enforces strictness on the address matching. No fuzzy matching."}, "max_radius_miles": {"type": "number", "description": "Accepts values 0.1 to 10.", "format": "float"}, "max_days_back": {"type": "integer", "description": "Specify 30 (days), 60 (days), 90 (days), etc. range for the recent sales comps that are returned.", "format": "int32"}, "max_results": {"type": "integer", "description": "Cap the amount of comps you want returned and how contribute to your AVM calculation.", "format": "int32"}, "arms_length": {"type": "boolean"}, "same_baths": {"type": "boolean", "description": "If true, all comps returned will have the same number of bathrooms"}, "same_beds": {"type": "boolean", "description": "If true, all comps returned will have the same number of bedrooms"}, "same_census_tract": {"type": "boolean", "description": "If true, all comps returned will reside inside the same census tract"}, "same_county": {"type": "boolean", "description": "If true, all comps returned will reside in the same county"}, "same_neighborhood": {"type": "boolean", "description": "If true, all comps returned will reside in the same neighborhood"}, "same_zip": {"type": "boolean", "description": "If true, all comps returned will reside in the same zipcode"}, "last_sale_price_min": {"type": "integer", "format": "int32"}, "last_sale_price_max": {"type": "integer", "format": "int32"}, "mls_listing_price_min": {"type": "integer", "format": "int32"}, "mls_listing_price_max": {"type": "integer", "format": "int32"}, "bathrooms_min": {"type": "integer", "description": "Set a range for the number of bathrooms in the house, so that comps returned match those specs", "format": "int32"}, "bathrooms_max": {"type": "integer", "format": "int32"}, "bathrooms_boost": {"type": "integer", "description": "Accepts values 1 to 50.", "format": "int32"}, "bedrooms_min": {"type": "integer", "description": "Set a range for the number of bedrooms in the house, so that comps returned match those specs", "format": "int32"}, "bedrooms_max": {"type": "integer", "format": "int32"}, "bedrooms_boost": {"type": "integer", "description": "Accepts values 1 to 50.", "format": "int32"}, "living_square_feet_min": {"type": "integer", "description": "Set a range for the square feet of the property, so that comps returned match the size specifications", "format": "int32"}, "living_square_feet_max": {"type": "integer", "format": "int32"}, "living_square_feet_boost": {"type": "integer", "description": "Accepts values 1 to 50.", "format": "int32"}, "lot_square_feet_min": {"type": "integer", "description": "Set a range for the square feet of the lot, so that comps returned match the size specifications", "format": "int32"}, "lot_square_feet_max": {"type": "integer", "format": "int32"}, "lot_square_feet_boost": {"type": "integer", "description": "Accepts values 1 to 50.", "format": "int32"}, "year_built_min": {"type": "integer", "description": "Set a range for the year your comparable properties were built", "format": "int32"}, "year_built_max": {"type": "integer", "format": "int32"}, "year_built_boost": {"type": "integer", "description": "Accepts values 1 to 50.", "format": "int32"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object"}}}}}, "deprecated": false}}, "/v2/PropGPT": {"post": {"summary": "PropGPT API", "description": "Check out the functionality of this endpoint at https://www.propgpt.com", "operationId": "propgpt-api", "parameters": [{"name": "x-api-key", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-user-id", "in": "header", "schema": {"type": "string"}}, {"name": "x-openai-key", "in": "header", "description": "Sign up with OpenAI (at https://openai.com/blog/openai-api) and get your free API Key to attach to all your requests to this endpoint for token spend tracking.", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["query"], "properties": {"size": {"type": "integer", "description": "Maximum of 250", "format": "int32"}, "query": {"type": "string", "description": "A natural language string that references data points in the Property Search API in order to retrieve a list of properties."}, "model": {"type": "string", "enum": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini"], "default": "gpt-4o", "description": "OpenAI model to use for query processing"}}}}}}, "responses": {"200": {"description": "200", "content": {"text/plain": {"schema": {"type": "object", "properties": {"live": {"type": "boolean", "example": true, "default": true}, "input": {"type": "object", "properties": {"mls_active": {"type": "boolean", "example": true, "default": true}, "mls_listing_price_min": {"type": "integer", "example": 600000, "default": 0}, "mls_listing_price_max": {"type": "integer", "example": 700000, "default": 0}, "city": {"type": "string", "example": "Herndon"}, "state": {"type": "string", "example": "VA"}, "query": {"type": "string", "example": "Find all properties listed for sale in Herndon Virginia between 600K and 700K"}, "size": {"type": "integer", "example": 3, "default": 0}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"absenteeOwner": {"type": "boolean", "example": false, "default": true}, "address": {"type": "object", "properties": {"address": {"type": "string", "example": "13011 Park Crescent Cir, Herndon, Va 20171"}, "city": {"type": "string", "example": "Herndon"}, "county": {"type": "string", "example": "Fairfax"}, "state": {"type": "string", "example": "VA"}, "street": {"type": "string", "example": "13011 Park Crescent Cir"}, "zip": {"type": "string", "example": "20171"}}}, "adjustableRate": {"type": "boolean", "example": false, "default": true}, "airConditioningAvailable": {"type": "boolean", "example": false, "default": true}, "assessedImprovementValue": {"type": "integer", "example": 372120, "default": 0}, "assessedLandValue": {"type": "integer", "example": 143000, "default": 0}, "assessedValue": {"type": "integer", "example": 515120, "default": 0}, "assumable": {"type": "boolean", "example": false, "default": true}, "auction": {"type": "boolean", "example": false, "default": true}, "basement": {"type": "boolean", "example": true, "default": true}, "bathrooms": {"type": "integer", "example": 2, "default": 0}, "bedrooms": {"type": "integer", "example": 3, "default": 0}, "cashBuyer": {"type": "boolean", "example": true, "default": true}, "corporateOwned": {"type": "boolean", "example": false, "default": true}, "death": {"type": "boolean", "example": false, "default": true}, "deck": {"type": "boolean", "example": true, "default": true}, "deckArea": {"type": "integer", "example": 252, "default": 0}, "documentType": {"type": "string", "example": "<PERSON>"}, "documentTypeCode": {"type": "string", "example": "DTGD"}, "equity": {"type": "boolean", "example": false, "default": true}, "equityPercent": {"type": "integer", "example": 100, "default": 0}, "estimatedEquity": {"type": "integer", "example": 553596, "default": 0}, "estimatedValue": {"type": "integer", "example": 553596, "default": 0}, "floodZone": {"type": "boolean", "example": true, "default": true}, "floodZoneDescription": {"type": "string", "example": "AREA OF MINIMAL FLOOD HAZARD"}, "floodZoneType": {"type": "string", "example": "X"}, "foreclosure": {"type": "boolean", "example": false, "default": true}, "forSale": {"type": "boolean", "example": false, "default": true}, "freeClear": {"type": "boolean", "example": false, "default": true}, "garage": {"type": "boolean", "example": false, "default": true}, "highEquity": {"type": "boolean", "example": true, "default": true}, "id": {"type": "string", "example": "33781421"}, "inherited": {"type": "boolean", "example": false, "default": true}, "inStateAbsenteeOwner": {"type": "boolean", "example": false, "default": true}, "investorBuyer": {"type": "boolean", "example": false, "default": true}, "judgment": {"type": "boolean", "example": false, "default": true}, "landUse": {"type": "string", "example": "Residential"}, "lastMortgage1Amount": {}, "lastSaleAmount": {"type": "string", "example": "520000"}, "lastSaleDate": {"type": "string", "example": "2020-07-14"}, "latitude": {"type": "number", "example": 38.946442, "default": 0}, "lenderName": {"type": "string", "example": "Suburban Federal Savings"}, "listingAmount": {}, "longitude": {"type": "number", "example": -77.394123, "default": 0}, "lotSquareFeet": {"type": "integer", "example": 1332, "default": 0}, "mailAddress": {"type": "object", "properties": {"address": {"type": "string", "example": "13011 Park Crescent Cir, Herndon, Va 20171"}, "city": {"type": "string", "example": "Herndon"}, "county": {"type": "string", "example": "Fairfax"}, "state": {"type": "string", "example": "VA"}, "street": {"type": "string", "example": "13011 Park Crescent Cir"}, "zip": {"type": "string", "example": "20171"}}}, "medianIncome": {"type": "string", "example": "150066"}, "MFH2to4": {"type": "boolean", "example": false, "default": true}, "MFH5plus": {"type": "boolean", "example": false, "default": true}, "mlsActive": {"type": "boolean", "example": true, "default": true}, "mlsCancelled": {"type": "boolean", "example": false, "default": true}, "mlsDaysOnMarket": {"type": "integer", "example": 43, "default": 0}, "mlsFailed": {"type": "boolean", "example": false, "default": true}, "mlsHasPhotos": {"type": "boolean", "example": true, "default": true}, "mlsLastSaleDate": {"type": "string", "example": "2020-07-14"}, "mlsListingDate": {"type": "string", "example": "2023-06-15"}, "mlsListingPrice": {"type": "integer", "example": 615000, "default": 0}, "mlsPending": {"type": "boolean", "example": true, "default": true}, "mlsSold": {"type": "boolean", "example": false, "default": true}, "mlsStatus": {"type": "string", "example": "Active"}, "mlsType": {"type": "string", "example": "ForSale"}, "negativeEquity": {"type": "boolean", "example": false, "default": true}, "neighborhood": {"type": "object", "properties": {"center": {"type": "string", "example": "POINT(-77.3937489163297 38.947147566096)"}, "id": {"type": "string", "example": "63187"}, "name": {"type": "string", "example": "Woodland Park"}, "type": {"type": "string", "example": "subdivision"}}}, "openMortgageBalance": {"type": "integer", "example": 0, "default": 0}, "outOfStateAbsenteeOwner": {"type": "boolean", "example": false, "default": true}, "owner1FirstName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "owner1LastName": {"type": "string", "example": "<PERSON>"}, "owner2FirstName": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "owner2LastName": {"type": "string", "example": "<PERSON>"}, "ownerOccupied": {"type": "boolean", "example": true, "default": true}, "pool": {"type": "boolean", "example": false, "default": true}, "poolArea": {"type": "integer", "example": 0, "default": 0}, "preForeclosure": {"type": "boolean", "example": false, "default": true}, "privateLender": {"type": "boolean", "example": false, "default": true}, "propertyId": {"type": "string", "example": "33781421"}, "propertyType": {"type": "string", "example": "SFR"}, "propertyUse": {"type": "string", "example": "Townhouse"}, "propertyUseCode": {"type": "integer", "example": 386, "default": 0}, "rentAmount": {}, "reo": {"type": "boolean", "example": false, "default": true}, "roomsCount": {"type": "integer", "example": 8, "default": 0}, "squareFeet": {"type": "integer", "example": 1656, "default": 0}, "stories": {"type": "integer", "example": 2, "default": 0}, "suggestedRent": {"type": "string", "example": "2590"}, "unitsCount": {"type": "integer", "example": 0, "default": 0}, "vacant": {"type": "boolean", "example": false, "default": true}, "yearBuilt": {"type": "integer", "example": 1999, "default": 0}, "yearsOwned": {"type": "integer", "example": 3, "default": 0}}}}, "resultCount": {"type": "integer", "example": 12, "default": 0}, "resultIndex": {"type": "integer", "example": 3, "default": 0}, "recordCount": {"type": "integer", "example": 3, "default": 0}, "statusCode": {"type": "integer", "example": 200, "default": 0}, "statusMessage": {"type": "string", "example": "Success"}, "requestExecutionTimeMS": {"type": "string", "example": "2121ms"}, "propGPTExecutionTimeMS": {"type": "string", "example": "2076ms"}, "searchExecutionTimeMS": {"type": "string", "example": "38ms"}}}}}}}, "deprecated": false}}, "/v2/CSVBuilder": {"post": {"summary": "CSV Generator API", "description": "", "operationId": "csv-generator-api", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file_name": {"type": "string", "description": "5-60 characters"}, "map": {"type": "array", "items": {"type": "string"}}, "webcomplete_url": {"type": "string"}, "count": {"type": "boolean"}, "ids": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "size": {"type": "integer", "format": "int32"}, "resultIndex": {"type": "integer", "format": "int32"}, "address": {"type": "string"}, "house": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "county": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "latitude": {"type": "number", "format": "float"}, "longitude": {"type": "number", "format": "float"}, "radius": {"type": "number", "format": "float"}, "polygon": {"type": "array", "items": {"properties": {"lat": {"type": "number", "format": "double"}, "lon": {"type": "number", "format": "double"}}, "type": "object"}}, "multi_polygon": {"type": "array", "items": {"properties": {"boundaries": {"type": "array", "items": {"properties": {"lat": {"type": "number", "format": "double"}, "lon": {"type": "number", "format": "double"}}, "type": "object"}}}, "type": "object"}}, "property_type": {"type": "string", "enum": ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"]}, "property_use_code": {"type": "integer", "format": "int32"}, "mls_active": {"type": "boolean"}, "mls_pending": {"type": "boolean"}, "mls_cancelled": {"type": "boolean"}, "mls_days_on_market_min": {"type": "integer", "format": "int32"}, "mls_days_on_market_max": {"type": "integer", "format": "int32"}, "mls_listing_price_min": {"type": "integer", "format": "int32"}, "mls_listing_price_max": {"type": "integer", "format": "int32"}, "absentee_owner": {"type": "boolean"}, "adjustable_rate": {"type": "boolean"}, "assumable": {"type": "boolean"}, "attic": {"type": "boolean"}, "auction": {"type": "boolean"}, "basement": {"type": "boolean"}, "breezeway": {"type": "boolean"}, "carport": {"type": "boolean"}, "cash_buyer": {"type": "boolean"}, "corporate_owned": {"type": "boolean"}, "death": {"type": "boolean"}, "deck": {"type": "boolean"}, "feature_balcony": {"type": "boolean"}, "fire_sprinklers": {"type": "boolean"}, "flood_zone": {"type": "boolean"}, "foreclosure": {"type": "boolean"}, "free_clear": {"type": "boolean"}, "garage": {"type": "boolean"}, "high_equity": {"type": "boolean"}, "inherited": {"type": "boolean"}, "in_state_owner": {"type": "boolean"}, "investor_buyer": {"type": "boolean"}, "judgment": {"type": "boolean"}, "mfh_2to4": {"type": "boolean"}, "mfh_5plus": {"type": "boolean"}, "negative_equity": {"type": "boolean"}, "out_of_state_owner": {"type": "boolean"}, "patio": {"type": "boolean"}, "pool": {"type": "boolean"}, "pre_foreclosure": {"type": "boolean"}, "prior_owner_individual": {"type": "boolean"}, "private_lender": {"type": "boolean"}, "quit_claim": {"type": "boolean"}, "reo": {"type": "boolean"}, "rv_parking": {"type": "boolean"}, "tax_lien": {"type": "boolean"}, "trust_owned": {"type": "boolean"}, "vacant": {"type": "boolean"}, "census_block": {"type": "string"}, "census_block_group": {"type": "string"}, "census_tract": {"type": "string"}, "construction": {"type": "string"}, "document_type_code": {"type": "string"}, "flood_zone_type": {"type": "string"}, "loan_type_code_first": {"type": "string"}, "loan_type_code_second": {"type": "string"}, "loan_type_code_third": {"type": "string"}, "notice_type": {"type": "string", "enum": ["FOR", "NOD", "NOL", "NTS", "REO"]}, "parcel_account_number": {"type": "string"}, "search_range": {"type": "string"}, "sewage": {"type": "string"}, "water_source": {"type": "string"}, "estimated_equity": {"type": "integer", "format": "int32"}, "equity_operator": {"type": "string", "enum": ["gt", "gte", "lt", "lte"]}, "equity_percent": {"type": "integer", "format": "int32"}, "equity_percent_operator": {"type": "string", "enum": ["gt", "gte", "lt", "lte"]}, "last_sale_date": {"type": "string", "format": "date"}, "last_sale_date_operator": {"type": "string", "enum": ["gt", "gte", "lt", "lte"]}, "median_income": {"type": "integer", "format": "int32"}, "median_income_operator": {"type": "string", "enum": ["gt", "gte", "lt", "lte"]}, "years_owned": {"type": "integer", "format": "int32"}, "years_owned_operator": {"type": "string", "enum": ["gt", "gte", "lt", "lte"]}, "assessed_improvement_value_min": {"type": "integer", "format": "int32"}, "assessed_improvement_value_max": {"type": "integer", "format": "int32"}, "assessed_land_value_min": {"type": "integer", "format": "int32"}, "assessed_land_value_max": {"type": "integer", "format": "int32"}, "assessed_value_min": {"type": "integer", "format": "int32"}, "assessed_value_max": {"type": "integer", "format": "int32"}, "auction_date_min": {"type": "string", "format": "date"}, "auction_date_max": {"type": "string", "format": "date"}, "baths_min": {"type": "integer", "format": "int32"}, "baths_max": {"type": "integer", "format": "int32"}, "beds_min": {"type": "integer", "format": "int32"}, "beds_max": {"type": "integer", "format": "int32"}, "building_size_min": {"type": "integer", "format": "int32"}, "building_size_max": {"type": "integer", "format": "int32"}, "deck_area_min": {"type": "integer", "format": "int32"}, "deck_area_max": {"type": "integer", "format": "int32"}, "estimated_equity_min": {"type": "integer", "format": "int32"}, "estimated_equity_max": {"type": "integer", "format": "int32"}, "foreclosure_date_min": {"type": "string", "format": "date"}, "foreclosure_date_max": {"type": "string", "format": "date"}, "last_sale_date_min": {"type": "string", "format": "date"}, "last_sale_date_max": {"type": "string", "format": "date"}, "last_sale_price_min": {"type": "integer", "format": "int32"}, "last_sale_price_max": {"type": "integer", "format": "int32"}, "lot_size_min": {"type": "integer", "format": "int32"}, "lot_size_max": {"type": "integer", "format": "int32"}, "ltv_min": {"type": "integer", "format": "int32"}, "ltv_max": {"type": "integer", "format": "int32"}, "median_income_min": {"type": "integer", "format": "int32"}, "median_income_max": {"type": "integer", "format": "int32"}, "mortgage_min": {"type": "integer", "format": "int32"}, "mortgage_max": {"type": "integer", "format": "int32"}, "rooms_min": {"type": "integer", "format": "int32"}, "rooms_max": {"type": "integer", "format": "int32"}, "pool_area_min": {"type": "integer", "format": "int32"}, "pool_area_max": {"type": "integer", "format": "int32"}, "portfolio_equity_min": {"type": "integer", "format": "int32"}, "portfolio_equity_max": {"type": "integer", "format": "int32"}, "portfolio_mortgage_balance_min": {"type": "integer", "format": "int32"}, "portfolio_mortgage_balance_max": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_min": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_max": {"type": "integer", "format": "int32"}, "portfolio_value_min": {"type": "integer", "format": "int32"}, "portfolio_value_max": {"type": "integer", "format": "int32"}, "pre_foreclosure_date_min": {"type": "string", "format": "date"}, "pre_foreclosure_date_max": {"type": "string", "format": "date"}, "prior_owner_months_owned_min": {"type": "integer", "format": "int32"}, "prior_owner_months_owned_max": {"type": "integer", "format": "int32"}, "properties_owned_min": {"type": "integer", "format": "int32"}, "properties_owned_max": {"type": "integer", "format": "int32"}, "stories_min": {"type": "integer", "format": "int32"}, "stories_max": {"type": "integer", "format": "int32"}, "tax_delinquent_year_min": {"type": "integer", "format": "int32"}, "tax_delinquent_year_max": {"type": "integer", "format": "int32"}, "units_min": {"type": "integer", "format": "int32"}, "units_max": {"type": "integer", "format": "int32"}, "value_min": {"type": "integer", "format": "int32"}, "value_max": {"type": "integer", "format": "int32"}, "year_built_min": {"type": "integer", "format": "int32"}, "year_built_max": {"type": "integer", "format": "int32"}, "years_owned_min": {"type": "integer", "format": "int32"}, "years_owned_max": {"type": "integer", "format": "int32"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object"}}}}}, "deprecated": false}}, "/v2/PropertyAvm": {"post": {"summary": "Lender Grade AVM API", "description": "Need the most precise Valuations for properties possible? Try out our Lender Grade AVM that uses statistical modeling, recent sales data, and market-to-market analysis to produce reliable AVMs", "operationId": "lender-grade-avm-api", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "address": {"type": "string"}, "strict": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object"}}}}}, "deprecated": false}}, "/v2/Reports/PropertyLiens": {"post": {"summary": "Involuntary Liens API", "description": "Go beyond our standard tax_liens & add Involuntary Lien Data to your Insights on a Property", "operationId": "involuntary-lien-api", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "address": {"type": "string"}, "zip": {"type": "string"}, "apn": {"type": "string"}, "fips": {"type": "string"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object"}}}}}, "deprecated": false}}, "/v2/PropertyMapping": {"post": {"summary": "[BETA] Mapping (\"Pins\") API", "description": "Have your PropTech Maps Come to Life with Unlimited \"Pins\" on a Map API. Only available on Growth+ Plans", "operationId": "beta-mapping-pins-api", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "resultIndex": {"type": "integer", "format": "int32"}, "address": {"type": "string", "description": "Only use if performing a radius search around a specific fully formatted address (123 Main St, Anywhere, TX 11111)"}, "house": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "county": {"type": "string"}, "latitude": {"type": "number", "format": "float"}, "longitude": {"type": "number", "format": "float"}, "radius": {"type": "number", "format": "float"}, "baths_min": {"type": "integer", "format": "int32"}, "baths_max": {"type": "integer", "format": "int32"}, "beds_min": {"type": "integer", "format": "int32"}, "beds_max": {"type": "integer", "format": "int32"}, "building_size_min": {"type": "integer", "format": "int32"}, "building_size_max": {"type": "integer", "format": "int32"}, "lot_size_min": {"type": "integer", "format": "int32"}, "lot_size_max": {"type": "integer", "format": "int32"}, "value_min": {"type": "integer", "format": "int32"}, "value_max": {"type": "integer", "format": "int32"}, "mls_active": {"type": "boolean"}, "mls_pending": {"type": "boolean"}, "mls_cancelled": {"type": "boolean"}, "mls_listing_price_min": {"type": "integer", "format": "int32"}, "mls_listing_price_max": {"type": "integer", "format": "int32"}, "mls_days_on_market_min": {"type": "integer", "format": "int32"}, "mls_days_market_max": {"type": "integer", "format": "int32"}, "polygon": {"type": "string"}, "multi_polygon": {"type": "string"}, "property_type": {"type": "string", "enum": ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"]}, "property_use_code": {"type": "integer", "description": "can also be an array of property use codes: [128, 392, etc.]", "format": "int32"}, "absentee_owner": {"type": "boolean"}, "adjustable_rate": {"type": "boolean"}, "assumable": {"type": "boolean"}, "attic": {"type": "boolean"}, "auction": {"type": "boolean"}, "auction_date_min": {"type": "string", "format": "date"}, "auction_date_max": {"type": "string", "format": "date"}, "basement": {"type": "boolean"}, "breezeway": {"type": "boolean"}, "carport": {"type": "boolean"}, "cash_buyer": {"type": "boolean"}, "corporate_owned": {"type": "boolean"}, "death": {"type": "boolean"}, "deck": {"type": "boolean"}, "deck_area_min": {"type": "integer", "format": "int32"}, "deck_area_max": {"type": "integer", "format": "int32"}, "feature_balcony": {"type": "boolean"}, "fire_sprinklers": {"type": "boolean"}, "flood_zone": {"type": "boolean"}, "flood_zone_type": {"type": "string"}, "foreclosure": {"type": "boolean"}, "foreclosure_date_min": {"type": "string", "format": "date"}, "foreclosure_date_max": {"type": "string", "format": "date"}, "free_clear": {"type": "boolean"}, "garage": {"type": "boolean"}, "high_equity": {"type": "boolean"}, "inherited": {"type": "boolean"}, "in_state_owner": {"type": "boolean"}, "investor_buyer": {"type": "boolean"}, "judgment": {"type": "boolean"}, "mfh_2to4": {"type": "boolean"}, "mfh_5plus": {"type": "boolean"}, "out_of_state_owner": {"type": "boolean"}, "patio": {"type": "boolean"}, "pool": {"type": "boolean"}, "pool_area_min": {"type": "integer", "format": "int32"}, "pool_area_max": {"type": "integer", "format": "int32"}, "pre_foreclosure": {"type": "boolean"}, "pre_foreclosure_date_min": {"type": "string", "format": "date"}, "pre_foreclosure_date_max": {"type": "string", "format": "date"}, "prior_owner_individual": {"type": "boolean"}, "prior_owner_months_owned_min": {"type": "integer", "format": "int32"}, "prior_owner_months_owned_max": {"type": "integer", "format": "int32"}, "private_lender": {"type": "boolean"}, "quit_claim": {"type": "boolean"}, "reo": {"type": "boolean"}, "rv_parking": {"type": "boolean"}, "tax_lien": {"type": "boolean"}, "trust_owned": {"type": "boolean"}, "vacant": {"type": "boolean"}, "construction": {"type": "string"}, "document_type_code": {"type": "string"}, "loan_type_code_first": {"type": "string"}, "loan_type_code_second": {"type": "string"}, "loan_type_code_third": {"type": "string"}, "notice_type": {"type": "string", "enum": ["FOR", "NOD", "NOL", "NTS", "REO"]}, "sewage": {"type": "string"}, "water_source": {"type": "string"}, "assessed_improvement_value_min": {"type": "integer", "format": "int32"}, "assessed_improvement_value_max": {"type": "integer", "format": "int32"}, "assessed_land_value_min": {"type": "integer", "format": "int32"}, "assessed_land_value_max": {"type": "integer", "format": "int32"}, "assessed_value_min": {"type": "integer", "format": "int32"}, "assessed_value_max": {"type": "integer", "format": "int32"}, "estimated_equity_min": {"type": "integer", "format": "int32"}, "estimated_equity_max": {"type": "integer", "format": "int32"}, "last_sale_date_min": {"type": "string", "format": "date"}, "last_sale_date_max": {"type": "string", "format": "date"}, "last_sale_price_min": {"type": "integer", "format": "int32"}, "last_sale_price_max": {"type": "integer", "format": "int32"}, "ltv_min": {"type": "integer", "format": "int32"}, "ltv_max": {"type": "integer", "format": "int32"}, "median_income_min": {"type": "integer", "format": "int32"}, "median_income_max": {"type": "integer", "format": "int32"}, "mortgage_min": {"type": "integer", "format": "int32"}, "mortgage_max": {"type": "integer", "format": "int32"}, "portfolio_equity_min": {"type": "integer", "format": "int32"}, "portfolio_equity_max": {"type": "integer", "format": "int32"}, "rooms_min": {"type": "integer", "format": "int32"}, "rooms_max": {"type": "integer", "format": "int32"}, "portfolio_mortgage_balance_min": {"type": "integer", "format": "int32"}, "portfolio_mortgage_balance_max": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_min": {"type": "integer", "format": "int32"}, "portfolio_purchased_last12_max": {"type": "integer", "format": "int32"}, "portfolio_purchased_last6_min": {"type": "integer", "format": "int32"}, "portfolio_purchased_last6_max": {"type": "integer", "format": "int32"}, "portfolio_value_min": {"type": "integer", "format": "int32"}, "portfolio_value_max": {"type": "integer", "format": "int32"}, "properties_owned_min": {"type": "integer", "format": "int32"}, "properties_owned_max": {"type": "integer", "format": "int32"}, "stories_min": {"type": "integer", "format": "int32"}, "stories_max": {"type": "integer", "format": "int32"}, "tax_delinquent_year_min": {"type": "integer", "format": "int32"}, "tax_delinquent_year_max": {"type": "integer", "format": "int32"}, "units_min": {"type": "integer", "format": "int32"}, "units_max": {"type": "integer", "format": "int32"}, "year_built_min": {"type": "integer", "format": "int32"}, "year_built_max": {"type": "integer", "format": "int32"}, "years_owned_min": {"type": "integer", "format": "int32"}, "years_owned_max": {"type": "integer", "format": "int32"}}}}}}, "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object"}}}}}, "deprecated": false}}, "/v2/AutoComplete": {"post": {"summary": "AutoComplete API", "description": "The AutoComplete approximates like property searches based on incomplete address parts and combinations. Our AutoComplete algorithms are powered by machine learning and give you rich property lists without having to design tons of different Property Search queries.", "operationId": "autocomplete-api", "parameters": [{"name": "x-api-key", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Autocomplete field."}, "searchType": {"type": "string", "description": "AutoComplete Field. A = full address ; C = city ; N = county; S = street ; Z = zip; G = neighborhood; T = state", "enum": ["A", "C", "N", "S", "Z", "G", "T"]}}}}}}, "responses": {"200": {"description": "AutoComplete suggestions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the suggestion"}, "title": {"type": "string", "description": "Display title for the suggestion", "example": "123 Main St, Arlington, VA 22205"}, "searchType": {"type": "string", "description": "Type of search result", "example": "A"}, "propertyId": {"type": "string", "description": "Property ID if this is a specific property"}, "apn": {"type": "string", "description": "Assessor's <PERSON><PERSON><PERSON>"}, "fips": {"type": "string", "description": "FIPS county code"}}}}, "statusCode": {"type": "integer", "example": 200}, "statusMessage": {"type": "string", "example": "Success"}}}}}}}, "deprecated": false}}, "/v1/PropertyParcel": {"post": {"summary": "Property Boundary API", "description": "Shape files API and property search API. All requests return the parcel boundaries in GEOJSON format. Quickly implement this API into your mapping applications.", "operationId": "property-parcel-api", "parameters": [{"name": "x-api-key", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-user-id", "in": "header", "description": "Denote a unique user identifier to this api call by passing it in this header field", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"address": {"type": "string", "description": "A fully formatted address with the following form: 123 Main St, City State Zip"}, "id": {"type": "integer", "description": "Property ID of an object returned by our Property Search API", "format": "int32"}, "zip": {"type": "string", "pattern": "^[0-9]{5}$", "minLength": 5, "maxLength": 5, "description": "5-digit ZIP code"}, "apn": {"type": "string", "description": "Assessor's <PERSON><PERSON><PERSON>"}, "fips": {"type": "string", "pattern": "^[0-9]{5}$", "minLength": 5, "maxLength": 5, "description": "5-digit FIPS county code"}}}}}}, "responses": {"200": {"description": "Property parcel boundaries retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "description": "GeoJSON FeatureCollection containing property boundaries", "properties": {"type": {"type": "string", "enum": ["FeatureCollection"]}, "features": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["Feature"]}, "geometry": {"type": "object", "description": "GeoJSON geometry object"}, "properties": {"type": "object", "description": "Property metadata"}}}}}}, "statusCode": {"type": "integer", "example": 200}, "statusMessage": {"type": "string", "example": "Success"}}}}}}}, "deprecated": false}}}}
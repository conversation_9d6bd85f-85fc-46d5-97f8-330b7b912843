Metadata-Version: 2.1
Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <g<PERSON><PERSON><PERSON><PERSON><EMAIL>>, layday <<EMAIL>>, <PERSON> <he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>
Requires-Python: >= 3.8
Description-Content-Type: text/markdown
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Dist: packaging >= 19.1
Requires-Dist: pyproject_hooks
Requires-Dist: colorama; os_name == "nt"
Requires-Dist: importlib-metadata >= 4.6; python_full_version < "3.10.2"
Requires-Dist: tomli >= 1.1.0; python_version < "3.11"
Requires-Dist: furo >= 2023.08.17 ; extra == "docs"
Requires-Dist: sphinx ~= 7.0 ; extra == "docs"
Requires-Dist: sphinx-argparse-cli >= 1.5 ; extra == "docs"
Requires-Dist: sphinx-autodoc-typehints >= 1.10 ; extra == "docs"
Requires-Dist: sphinx-issues >= 3.0.0 ; extra == "docs"
Requires-Dist: build[uv, virtualenv] ; extra == "test"
Requires-Dist: filelock >= 3 ; extra == "test"
Requires-Dist: pytest >= 6.2.4 ; extra == "test"
Requires-Dist: pytest-cov >= 2.12 ; extra == "test"
Requires-Dist: pytest-mock >= 2 ; extra == "test"
Requires-Dist: pytest-rerunfailures >= 9.1 ; extra == "test"
Requires-Dist: pytest-xdist >= 1.34 ; extra == "test"
Requires-Dist: wheel >= 0.36.0 ; extra == "test"
Requires-Dist: setuptools >= 42.0.0 ; extra == "test" and ( python_version < "3.10")
Requires-Dist: setuptools >= 56.0.0 ; extra == "test" and ( python_version == "3.10")
Requires-Dist: setuptools >= 56.0.0 ; extra == "test" and ( python_version == "3.11")
Requires-Dist: setuptools >= 67.8.0 ; extra == "test" and ( python_version >= "3.12")
Requires-Dist: build[uv] ; extra == "typing"
Requires-Dist: importlib-metadata >= 5.1 ; extra == "typing"
Requires-Dist: mypy ~= 1.9.0 ; extra == "typing"
Requires-Dist: tomli ; extra == "typing"
Requires-Dist: typing-extensions >= 3.7.4.3 ; extra == "typing"
Requires-Dist: uv >= 0.1.18 ; extra == "uv"
Requires-Dist: virtualenv >= 20.0.35 ; extra == "virtualenv"
Project-URL: changelog, https://build.pypa.io/en/stable/changelog.html
Project-URL: homepage, https://build.pypa.io
Project-URL: issues, https://github.com/pypa/build/issues
Project-URL: source, https://github.com/pypa/build
Provides-Extra: docs
Provides-Extra: test
Provides-Extra: typing
Provides-Extra: uv
Provides-Extra: virtualenv

# build

[![pre-commit.ci status](https://results.pre-commit.ci/badge/github/pypa/build/main.svg)](https://results.pre-commit.ci/latest/github/pypa/build/main)
[![CI test](https://github.com/pypa/build/actions/workflows/test.yml/badge.svg)](https://github.com/pypa/build/actions/workflows/test.yml)
[![codecov](https://codecov.io/gh/pypa/build/branch/main/graph/badge.svg)](https://codecov.io/gh/pypa/build)

[![Documentation Status](https://readthedocs.org/projects/pypa-build/badge/?version=latest)](https://build.pypa.io/en/latest/?badge=latest)
[![PyPI version](https://badge.fury.io/py/build.svg)](https://pypi.org/project/build/)
[![Discord](https://img.shields.io/discord/803025117553754132?label=Discord%20chat%20%23build)](https://discord.gg/pypa)

A simple, correct Python build frontend.

See the [documentation](https://build.pypa.io) for more information.

### Installation

`build` can be installed via `pip` or an equivalent via:

```console
$ pip install build
```

### Usage

```console
$ python -m build
```

This will build the package in an isolated environment, generating a
source-distribution and wheel in the directory `dist/`.
See the [documentation](https://build.pypa.io) for full information.

### Common arguments

- `--sdist` (`-s`): Produce just an SDist
- `--wheel` (`-w`): Produce just a wheel
- `-C<option>=<value>`: A Config-setting, the PEP 517 way of passing options to a backend. Can be passed multiple times. Matching options will make a list. Note that setuptools has very limited support.
- `--installer`: Pick an installer for the isolated build (`pip` or `uv`).
- `--no-isolation` (`-n`): Disable build isolation.
- `--skip-dependency-check` (`-x`): Disable dependency checking when not isolated; this should be done if some requirements or version ranges are not required for non-isolated builds.
- `--outdir` (`-o`): The output directory (defaults to `dist`)

Some common combinations of arguments:

- `--sdist --wheel` (`-sw`): Produce and SDist and a wheel, both from the source distribution. The default (if no flag is passed) is to build an SDist and then build a wheel _from_ the SDist.
- `-nx`: Disable build isolation and dependency checking. Identical to pip and uv's `--no-build-isolation` flag.

### Integration with other tools

#### pipx

If you use [pipx][], such as in GitHub Actions, the following command will download
and run build in one step:

```console
$ pipx run build
```

#### uv

If you want to use [uv][] to speed up the virtual environment creation, you can use
`--installer=uv`. You can get a Python wheel for `uv` with the `[uv]` extra.
Combining both suggestions yields the following:

```console
$ pipx run build[uv] --installer=uv
```

#### cibuildwheel

If you are using [cibuildwheel][], build is integrated and can be use with either (in your `pyproject.toml`):

```toml
[tool.cibuildwheel]
build-frontend = "build"
```

or

```toml
[tool.cibuildwheel]
build-frontend = "build[uv]"
```

(Be sure to pre-install uv before running cibuildwheel for this one!)

#### Conda-forge

On conda-forge, this package is called [python-build][].

### Code of Conduct

Everyone interacting in the build's codebase, issue trackers, chat rooms, and mailing lists is expected to follow
the [PSF Code of Conduct].

[psf code of conduct]: https://github.com/pypa/.github/blob/main/CODE_OF_CONDUCT.md
[pipx]: https://pipx.pypa.io
[uv]: https://docs.astral.sh/uv/
[cibuildwheel]: https://cibuildwheel.pypa.io
[python-build]: https://github.com/conda-forge/python-build-feedstock


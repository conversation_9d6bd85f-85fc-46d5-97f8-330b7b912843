msgpack-1.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
msgpack-1.1.1.dist-info/METADATA,sha256=xR8Th3-hQ5cUO1ANM5u28wpE2SDUViUoTojrjQd1rBA,8365
msgpack-1.1.1.dist-info/RECORD,,
msgpack-1.1.1.dist-info/WHEEL,sha256=oqGJCpG61FZJmvyZ3C_0aCv-2mdfcY9e3fXvyUNmWfM,136
msgpack-1.1.1.dist-info/licenses/COPYING,sha256=SS3tuoXaWHL3jmCRvNH-pHTWYNNay03ulkuKqz8AdCc,614
msgpack-1.1.1.dist-info/top_level.txt,sha256=2tykSY1pXdiA2xYTDR6jPw0qI5ZGxRihyhf4S5hZyXk,8
msgpack/__init__.py,sha256=q2T5PRsITVpeytgS0WiSqok9IY8V_aFiC8VVlXTCTgA,1109
msgpack/__pycache__/__init__.cpython-313.pyc,,
msgpack/__pycache__/exceptions.cpython-313.pyc,,
msgpack/__pycache__/ext.cpython-313.pyc,,
msgpack/__pycache__/fallback.cpython-313.pyc,,
msgpack/_cmsgpack.cpython-313-darwin.so,sha256=BYm3eR_IfNyLOYJLsw2SqoNwPWqOK7rAyWhzLAhNkW0,192368
msgpack/exceptions.py,sha256=dCTWei8dpkrMsQDcjQk74ATl9HsIBH0ybt8zOPNqMYc,1081
msgpack/ext.py,sha256=kteJv03n9tYzd5oo3xYopVTo4vRaAxonBQQJhXohZZo,5726
msgpack/fallback.py,sha256=0g1Pzp0vtmBEmJ5w9F3s_-JMVURP8RS4G1cc5TRaAsI,32390

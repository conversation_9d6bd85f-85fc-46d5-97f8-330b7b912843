# PropBolt Data API - Google Cloud App Engine Configuration
# Python-based data processing and analytics API for data.propbolt.com
# Auto-scaling with load balancing for enterprise workloads

runtime: python39
service: data

# Auto-scaling configuration for enterprise workloads
automatic_scaling:
  min_instances: 1
  max_instances: 100
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

# Resource allocation for data processing
resources:
  cpu: 2
  memory_gb: 4
  disk_size_gb: 10

# Environment variables
env_variables:
  # Server Configuration
  PORT: 8081
  FLASK_ENV: production
  
  # Database Configuration (Google Cloud PostgreSQL)
  DB_HOST: *************
  DB_PORT: 5432
  DB_NAME: propbolt
  DB_USER: propbolt_user
  DB_PASSWORD: PropBolt2024!
  DB_SSL_MODE: require
  DATABASE_URL: ***********************************************************/propbolt?sslmode=require
  
  # External API Configuration
  REAL_ESTATE_API_KEY: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
  REAL_ESTATE_API_URL: https://api.realestateapi.com/v2
  
  # Google Cloud Configuration
  GCP_PROJECT_ID: gold-braid-458901-v2
  GCP_PROJECT_NUMBER: ************
  GCP_REGION: us-central1
  GOOGLE_APPLICATION_CREDENTIALS: propbolt-service-account-key.json
  GOOGLE_MAPS_API_KEY: AIzaSyBbnrFdaQisjmNJzviY9s9GrgxQvAdM6k4
  MAPTILER_API_KEY: wBfumy70aE1pm6PGKkiU
  
  # CORS Configuration
  CORS_ALLOWED_ORIGINS: https://propbolt.com,https://go.propbolt.com,https://api.propbolt.com,http://localhost:3000
  CORS_ALLOW_CREDENTIALS: true
  
  # Feature Flags
  FEATURE_DATA_ANALYTICS: true
  FEATURE_BULK_PROCESSING: true
  FEATURE_EXPORT: true
  
  # Logging Configuration
  LOG_LEVEL: info
  DEBUG_MODE: false

# Health check configuration
readiness_check:
  path: "/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Network configuration
network:
  forwarded_ports:
    - 8081

# Security headers
handlers:
- url: /.*
  script: auto
  secure: always
  redirect_http_response_code: 301

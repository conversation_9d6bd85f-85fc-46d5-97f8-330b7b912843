# PropBolt Real Estate API

Complete 13 Endpoint Implementation for `data.propbolt.com` - Python Flask-based real estate data API.

## Overview

The PropBolt Real Estate API provides comprehensive real estate data services including property search, details, comparables, AVM, and AI-powered insights. It's built with Python Flask and deployed on Google Cloud App Engine with auto-scaling capabilities.

## Features

- **Complete Real Estate Data**: 13 comprehensive endpoints
- **Property Search & Details**: Advanced property information retrieval
- **Comparables Analysis**: Property comparison data (v2 & v3)
- **AVM (Automated Valuation Model)**: Property value estimation
- **AI-Powered PropGPT**: Intelligent property insights
- **Address Services**: Autocomplete and verification
- **Reports & Analytics**: Property liens and mapping
- **Real-time Health Monitoring**: Built-in health checks for Google Cloud
- **Auto-scaling**: Enterprise-grade scaling with load balancing

## API Endpoints

### Core Services
- `GET /` - API information and endpoint listing
- `GET /health` - Service health status
- `GET /docs` - Interactive API documentation (Swagger UI)

### Property Services
- `POST /v2/PropertySearch` - Search for properties
- `POST /v2/PropertyDetail` - Get detailed property information
- `POST /v2/PropertyDetailBulk` - Bulk property details retrieval
- `POST /v1/PropertyParcel` - Property parcel information

### Comparables & Valuation
- `POST /v2/PropertyComps` - Property comparables (v2)
- `POST /v3/PropertyComps` - Property comparables (v3)
- `POST /v2/PropertyAvm` - Automated Valuation Model

### Address Services
- `POST /v2/AutoComplete` - Address autocomplete
- `POST /v2/AddressVerification` - Address verification

### Advanced Features
- `POST /v2/PropGPT` - AI-powered property insights
- `POST /v2/CSVBuilder` - CSV data builder
- `POST /v2/Reports/PropertyLiens` - Property liens reports
- `POST /v2/PropertyMapping` - Property mapping services

## Configuration

Environment variables are configured in `.env` and `app.yaml`:

- `PORT`: Server port (default: 8081)
- `FLASK_ENV`: Flask environment (production)
- `DATABASE_URL`: PostgreSQL connection string
- `REAL_ESTATE_API_KEY`: External API key
- `CORS_ALLOWED_ORIGINS`: Allowed CORS origins

## Database

Uses the same Google Cloud PostgreSQL database as the main API:
- Host: *************
- Database: propbolt
- SSL required

## Deployment

The service is automatically deployed with the main deployment script:

```bash
./deploy-gcp.sh
```

Or deploy individually:

```bash
cd data
gcloud app deploy app.yaml --quiet --promote
```

## Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run the server:
```bash
python main.py
```

The server will start on `http://localhost:8081`

## Architecture

- **Runtime**: Python 3.9
- **Framework**: Flask with CORS support
- **Database**: PostgreSQL with psycopg2
- **Deployment**: Google Cloud App Engine
- **Auto-scaling**: 1-100 instances based on CPU/throughput
- **Resources**: 2 CPU, 4GB RAM per instance

## Security

- HTTPS enforced
- CORS configured for allowed origins
- Database connections use SSL
- Google Cloud service account authentication

## Monitoring

- Health checks at `/health`
- Structured logging with configurable levels
- Google Cloud monitoring integration

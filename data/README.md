# PropBolt Data API

Python Flask-based data processing and analytics API for `data.propbolt.com`.

## Overview

The Data API provides advanced data processing, analytics, and bulk operations for the PropBolt platform. It's built with Python Flask and deployed on Google Cloud App Engine with auto-scaling capabilities.

## Features

- **Property Analytics**: Comprehensive property data analysis
- **Bulk Processing**: Handle large datasets efficiently
- **Data Export**: Export property data in various formats
- **Real-time Health Monitoring**: Built-in health checks for Google Cloud
- **Auto-scaling**: Enterprise-grade scaling with load balancing

## API Endpoints

### Health Check
- `GET /health` - Service health status

### Analytics
- `GET /api/v1/analytics/properties` - Get property analytics data

### Export
- `GET /api/v1/export/properties` - Export properties data
  - Query parameters: `limit`, `offset`

### Bulk Operations
- `POST /api/v1/bulk/process` - Bulk process property data
  - Body: `{"addresses": ["address1", "address2", ...]}`

## Configuration

Environment variables are configured in `.env` and `app.yaml`:

- `PORT`: Server port (default: 8081)
- `FLASK_ENV`: Flask environment (production)
- `DATABASE_URL`: PostgreSQL connection string
- `REAL_ESTATE_API_KEY`: External API key
- `CORS_ALLOWED_ORIGINS`: Allowed CORS origins

## Database

Uses the same Google Cloud PostgreSQL database as the main API:
- Host: *************
- Database: propbolt
- SSL required

## Deployment

The service is automatically deployed with the main deployment script:

```bash
./deploy-gcp.sh
```

Or deploy individually:

```bash
cd data
gcloud app deploy app.yaml --quiet --promote
```

## Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run the server:
```bash
python main.py
```

The server will start on `http://localhost:8081`

## Architecture

- **Runtime**: Python 3.9
- **Framework**: Flask with CORS support
- **Database**: PostgreSQL with psycopg2
- **Deployment**: Google Cloud App Engine
- **Auto-scaling**: 1-100 instances based on CPU/throughput
- **Resources**: 2 CPU, 4GB RAM per instance

## Security

- HTTPS enforced
- CORS configured for allowed origins
- Database connections use SSL
- Google Cloud service account authentication

## Monitoring

- Health checks at `/health`
- Structured logging with configurable levels
- Google Cloud monitoring integration

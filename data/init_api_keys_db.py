#!/usr/bin/env python3
"""
PropBolt API Key Management - Database Initialization
Initialize the database with API key management tables and sample data
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from api_key_manager import APIKeyManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection using environment variables"""
    db_config = {
        'host': os.getenv('DB_HOST'),
        'port': os.getenv('DB_PORT', 5432),
        'database': os.getenv('DB_NAME'),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'sslmode': os.getenv('DB_SSL_MODE', 'require')
    }
    
    try:
        conn = psycopg2.connect(**db_config)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

def read_sql_file(filename):
    """Read SQL file content"""
    try:
        with open(filename, 'r') as file:
            return file.read()
    except Exception as e:
        logger.error(f"Failed to read SQL file {filename}: {e}")
        return None

def initialize_database():
    """Initialize the database with API key management tables"""
    logger.info("🔌 Connecting to database...")
    
    conn = get_db_connection()
    if not conn:
        logger.error("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor()
        
        logger.info("📋 Creating API key management tables...")
        
        # Read and execute the database schema
        schema_sql = read_sql_file('database_schema.sql')
        if not schema_sql:
            logger.error("❌ Failed to read database schema file")
            return False
        
        cursor.execute(schema_sql)
        conn.commit()
        
        logger.info("✅ API key management tables created successfully!")
        
        # Verify tables were created
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('api_keys', 'api_usage', 'daily_usage_summary', 'rate_limit_windows')
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        logger.info("📋 API Key Management tables created:")
        for table in tables:
            logger.info(f"  - {table[0]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def create_sample_api_keys():
    """Create sample API keys for testing"""
    logger.info("🔑 Creating sample API keys...")
    
    try:
        api_key_manager = APIKeyManager()
        
        # Check if admin user exists
        conn = get_db_connection()
        if not conn:
            logger.error("❌ Failed to connect to database for sample keys")
            return False
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT id FROM "user" WHERE email = %s', ('<EMAIL>',))
        admin_user = cursor.fetchone()
        
        if not admin_user:
            logger.warning("⚠️  Admin user not found. Creating sample keys with placeholder user ID.")
            user_id = 'admin-001'  # Use the default admin ID from init-database.sql
        else:
            user_id = admin_user['id']
        
        cursor.close()
        conn.close()
        
        # Create sample API keys for different tiers
        sample_keys = [
            {
                'name': 'Development Key',
                'tier': 'basic',
                'key_type': 'test'
            },
            {
                'name': 'Production Basic Key',
                'tier': 'basic',
                'key_type': 'live'
            },
            {
                'name': 'Premium Key',
                'tier': 'premium',
                'key_type': 'live'
            }
        ]
        
        created_keys = []
        for key_config in sample_keys:
            try:
                key_info = api_key_manager.create_api_key(
                    user_id=user_id,
                    name=key_config['name'],
                    tier=key_config['tier'],
                    key_type=key_config['key_type']
                )
                created_keys.append(key_info)
                logger.info(f"✅ Created {key_config['name']}: {key_info['key_prefix']}...")
            except Exception as e:
                logger.error(f"❌ Failed to create {key_config['name']}: {e}")
        
        if created_keys:
            logger.info("🎉 Sample API keys created successfully!")
            logger.info("📋 Created keys:")
            for key in created_keys:
                logger.info(f"  - {key['name']} ({key['tier']}): {key['api_key']}")
            logger.info("⚠️  Save these API keys securely - they won't be shown again!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create sample API keys: {e}")
        return False

def main():
    """Main initialization function"""
    logger.info("🚀 PropBolt API Key Management - Database Initialization")
    logger.info("=" * 60)
    
    # Check environment variables
    required_env_vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set the required environment variables and try again.")
        sys.exit(1)
    
    # Initialize database
    if not initialize_database():
        logger.error("❌ Database initialization failed")
        sys.exit(1)
    
    # Create sample API keys
    create_sample = input("\n🔑 Create sample API keys for testing? (y/N): ").lower().strip()
    if create_sample in ['y', 'yes']:
        if not create_sample_api_keys():
            logger.warning("⚠️  Sample API key creation failed, but database initialization was successful")
    
    logger.info("")
    logger.info("🎉 Database initialization completed successfully!")
    logger.info("📋 Next steps:")
    logger.info("1. Test the API key endpoints using the admin routes")
    logger.info("2. Generate API keys for your users")
    logger.info("3. Test the protected endpoints with valid API keys")
    logger.info("4. Monitor usage through the analytics endpoints")

if __name__ == '__main__':
    main()

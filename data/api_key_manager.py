#!/usr/bin/env python3
"""
PropBolt API Key Management System
Secure API key generation, validation, and management for data.propbolt.com
"""

import os
import hashlib
import secrets
import string
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class APIKeyManager:
    """Manages API keys for PropBolt Real Estate API"""
    
    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST'),
            'port': os.getenv('DB_PORT', 5432),
            'database': os.getenv('DB_NAME'),
            'user': os.getenv('DB_USER'),
            'password': os.getenv('DB_PASSWORD'),
            'sslmode': os.getenv('DB_SSL_MODE', 'require')
        }
        
        # Default tier configurations
        self.tier_configs = {
            'basic': {
                'requests_per_minute': 60,
                'requests_per_day': 1000,
                'allowed_endpoints': None  # All endpoints
            },
            'premium': {
                'requests_per_minute': 300,
                'requests_per_day': 10000,
                'allowed_endpoints': None
            },
            'enterprise': {
                'requests_per_minute': 1000,
                'requests_per_day': 100000,
                'allowed_endpoints': None
            }
        }
    
    def get_db_connection(self) -> Optional[psycopg2.extensions.connection]:
        """Get database connection with error handling"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def generate_api_key(self, key_type: str = 'live') -> str:
        """Generate a secure API key with PropBolt prefix"""
        if key_type not in ['live', 'test']:
            raise ValueError("key_type must be 'live' or 'test'")
        
        prefix = f"pb_{key_type}_"
        # Generate 32 character random string
        alphabet = string.ascii_letters + string.digits
        random_part = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        return f"{prefix}{random_part}"
    
    def hash_api_key(self, api_key: str) -> str:
        """Create SHA-256 hash of API key for secure storage"""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def create_api_key(self, user_id: str, name: str, tier: str = 'basic', 
                      key_type: str = 'live', expires_days: Optional[int] = None) -> Dict:
        """Create a new API key for a user"""
        if tier not in self.tier_configs:
            raise ValueError(f"Invalid tier: {tier}")
        
        conn = self.get_db_connection()
        if not conn:
            raise Exception("Database connection failed")
        
        try:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Generate API key
            api_key = self.generate_api_key(key_type)
            key_hash = self.hash_api_key(api_key)
            key_prefix = api_key[:8]  # First 8 characters for identification
            
            # Calculate expiration date
            expires_at = None
            if expires_days:
                expires_at = datetime.utcnow() + timedelta(days=expires_days)
            
            # Get tier configuration
            tier_config = self.tier_configs[tier]
            
            # Insert API key
            cursor.execute("""
                INSERT INTO api_keys (
                    key_hash, key_prefix, user_id, name, tier, 
                    expires_at, requests_per_minute, requests_per_day, allowed_endpoints
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id, created_at
            """, (
                key_hash, key_prefix, user_id, name, tier,
                expires_at, tier_config['requests_per_minute'],
                tier_config['requests_per_day'], tier_config['allowed_endpoints']
            ))
            
            result = cursor.fetchone()
            conn.commit()
            
            return {
                'id': result['id'],
                'api_key': api_key,  # Only returned once during creation
                'key_prefix': key_prefix,
                'name': name,
                'tier': tier,
                'created_at': result['created_at'],
                'expires_at': expires_at,
                'requests_per_minute': tier_config['requests_per_minute'],
                'requests_per_day': tier_config['requests_per_day']
            }
            
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to create API key: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """Validate API key and return key information if valid"""
        if not api_key or not api_key.startswith(('pb_live_', 'pb_test_')):
            return None
        
        conn = self.get_db_connection()
        if not conn:
            return None
        
        try:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            key_hash = self.hash_api_key(api_key)
            
            cursor.execute("""
                SELECT ak.*, u.email as user_email, u.name as user_name
                FROM api_keys ak
                LEFT JOIN "user" u ON ak.user_id = u.id
                WHERE ak.key_hash = %s 
                AND ak.is_active = TRUE 
                AND (ak.expires_at IS NULL OR ak.expires_at > CURRENT_TIMESTAMP)
                AND ak.revoked_at IS NULL
            """, (key_hash,))
            
            key_info = cursor.fetchone()
            
            if key_info:
                # Update last_used_at
                cursor.execute("""
                    UPDATE api_keys SET last_used_at = CURRENT_TIMESTAMP 
                    WHERE id = %s
                """, (key_info['id'],))
                conn.commit()
                
                return dict(key_info)
            
            return None
            
        except Exception as e:
            logger.error(f"API key validation failed: {e}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    def check_rate_limit(self, key_id: str, requests_per_minute: int) -> Tuple[bool, int]:
        """Check if API key is within rate limits. Returns (allowed, remaining_requests)"""
        conn = self.get_db_connection()
        if not conn:
            return False, 0
        
        try:
            cursor = conn.cursor()
            current_minute = datetime.utcnow().replace(second=0, microsecond=0)
            
            # Get or create rate limit window
            cursor.execute("""
                INSERT INTO rate_limit_windows (key_id, window_start, window_type, request_count)
                VALUES (%s, %s, 'minute', 1)
                ON CONFLICT (key_id, window_start, window_type) 
                DO UPDATE SET 
                    request_count = rate_limit_windows.request_count + 1,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING request_count
            """, (key_id, current_minute))
            
            result = cursor.fetchone()
            current_count = result[0] if result else 1
            
            conn.commit()
            
            allowed = current_count <= requests_per_minute
            remaining = max(0, requests_per_minute - current_count)
            
            return allowed, remaining
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return False, 0
        finally:
            cursor.close()
            conn.close()
    
    def log_api_usage(self, key_id: str, endpoint: str, method: str, 
                     response_status: int, response_time_ms: Optional[int] = None,
                     ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                     request_size: Optional[int] = None, response_size: Optional[int] = None,
                     error_message: Optional[str] = None):
        """Log API usage for analytics and billing"""
        conn = self.get_db_connection()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO api_usage (
                    key_id, endpoint, method, response_status, response_time_ms,
                    ip_address, user_agent, request_size, response_size, error_message
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                key_id, endpoint, method, response_status, response_time_ms,
                ip_address, user_agent, request_size, response_size, error_message
            ))
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")
        finally:
            cursor.close()
            conn.close()
    
    def get_api_keys(self, user_id: Optional[str] = None) -> List[Dict]:
        """Get API keys for a user or all keys (admin only)"""
        conn = self.get_db_connection()
        if not conn:
            return []
        
        try:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            if user_id:
                cursor.execute("""
                    SELECT ak.*, u.email as user_email, u.name as user_name,
                           dus.total_requests as daily_requests,
                           dus.date as last_usage_date
                    FROM api_keys ak
                    LEFT JOIN "user" u ON ak.user_id = u.id
                    LEFT JOIN daily_usage_summary dus ON ak.id = dus.key_id 
                        AND dus.date = CURRENT_DATE
                    WHERE ak.user_id = %s
                    ORDER BY ak.created_at DESC
                """, (user_id,))
            else:
                cursor.execute("""
                    SELECT ak.*, u.email as user_email, u.name as user_name,
                           dus.total_requests as daily_requests,
                           dus.date as last_usage_date
                    FROM api_keys ak
                    LEFT JOIN "user" u ON ak.user_id = u.id
                    LEFT JOIN daily_usage_summary dus ON ak.id = dus.key_id 
                        AND dus.date = CURRENT_DATE
                    ORDER BY ak.created_at DESC
                """)
            
            keys = cursor.fetchall()
            return [dict(key) for key in keys]
            
        except Exception as e:
            logger.error(f"Failed to get API keys: {e}")
            return []
        finally:
            cursor.close()
            conn.close()
    
    def revoke_api_key(self, key_id: str, user_id: Optional[str] = None) -> bool:
        """Revoke an API key"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # If user_id is provided, ensure the key belongs to the user
            if user_id:
                cursor.execute("""
                    UPDATE api_keys 
                    SET is_active = FALSE, revoked_at = CURRENT_TIMESTAMP
                    WHERE id = %s AND user_id = %s
                """, (key_id, user_id))
            else:
                cursor.execute("""
                    UPDATE api_keys 
                    SET is_active = FALSE, revoked_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (key_id,))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            logger.error(f"Failed to revoke API key: {e}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    def get_usage_analytics(self, key_id: str, days: int = 30) -> Dict:
        """Get usage analytics for an API key"""
        conn = self.get_db_connection()
        if not conn:
            return {}
        
        try:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get daily usage summary
            cursor.execute("""
                SELECT date, total_requests, successful_requests, failed_requests,
                       avg_response_time_ms, endpoints_used
                FROM daily_usage_summary
                WHERE key_id = %s AND date >= CURRENT_DATE - INTERVAL '%s days'
                ORDER BY date DESC
            """, (key_id, days))
            
            daily_usage = [dict(row) for row in cursor.fetchall()]
            
            # Get total statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN response_status < 400 THEN 1 END) as successful_requests,
                    COUNT(CASE WHEN response_status >= 400 THEN 1 END) as failed_requests,
                    AVG(response_time_ms) as avg_response_time,
                    COUNT(DISTINCT endpoint) as unique_endpoints
                FROM api_usage
                WHERE key_id = %s AND timestamp >= CURRENT_TIMESTAMP - INTERVAL '%s days'
            """, (key_id, days))
            
            totals = dict(cursor.fetchone())
            
            return {
                'daily_usage': daily_usage,
                'totals': totals,
                'period_days': days
            }
            
        except Exception as e:
            logger.error(f"Failed to get usage analytics: {e}")
            return {}
        finally:
            cursor.close()
            conn.close()

#!/usr/bin/env python3
"""
PropBolt Data API - Python Flask Application
Enterprise-grade data processing and analytics API for data.propbolt.com
"""

import os
import logging
from flask import Flask, jsonify, request
from flask_cors import CORS
import psycopg2
from psycopg2.extras import RealDictCursor
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
cors_origins = os.getenv('CORS_ALLOWED_ORIGINS', 'http://localhost:3000').split(',')
CORS(app, origins=cors_origins, supports_credentials=True)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'port': os.getenv('DB_PORT', 5432),
    'database': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'sslmode': os.getenv('DB_SSL_MODE', 'require')
}

# External API configuration
REAL_ESTATE_API_KEY = os.getenv('REAL_ESTATE_API_KEY')
REAL_ESTATE_API_URL = os.getenv('REAL_ESTATE_API_URL')

def get_db_connection():
    """Get database connection with error handling"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Google Cloud"""
    try:
        # Test database connection
        conn = get_db_connection()
        if conn:
            conn.close()
            return jsonify({
                'status': 'healthy',
                'service': 'data-api',
                'timestamp': datetime.utcnow().isoformat(),
                'database': 'connected'
            }), 200
        else:
            return jsonify({
                'status': 'unhealthy',
                'service': 'data-api',
                'timestamp': datetime.utcnow().isoformat(),
                'database': 'disconnected'
            }), 503
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'service': 'data-api',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }), 503

@app.route('/api/v1/analytics/properties', methods=['GET'])
def get_property_analytics():
    """Get property analytics data"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get property analytics
        cursor.execute("""
            SELECT 
                COUNT(*) as total_properties,
                AVG(price) as avg_price,
                MIN(price) as min_price,
                MAX(price) as max_price,
                COUNT(DISTINCT city) as unique_cities,
                COUNT(DISTINCT state) as unique_states
            FROM properties 
            WHERE price > 0
        """)
        
        analytics = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': dict(analytics) if analytics else {},
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Analytics query failed: {e}")
        return jsonify({'error': 'Analytics query failed', 'details': str(e)}), 500

@app.route('/api/v1/export/properties', methods=['GET'])
def export_properties():
    """Export properties data"""
    try:
        limit = request.args.get('limit', 1000, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        cursor.execute("""
            SELECT * FROM properties 
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """, (limit, offset))
        
        properties = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': [dict(prop) for prop in properties],
            'count': len(properties),
            'limit': limit,
            'offset': offset,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Export query failed: {e}")
        return jsonify({'error': 'Export query failed', 'details': str(e)}), 500

@app.route('/api/v1/bulk/process', methods=['POST'])
def bulk_process():
    """Bulk process property data"""
    try:
        data = request.get_json()
        if not data or 'addresses' not in data:
            return jsonify({'error': 'Invalid request data'}), 400
        
        addresses = data['addresses']
        results = []
        
        for address in addresses:
            # Process each address (placeholder for actual processing logic)
            result = {
                'address': address,
                'status': 'processed',
                'timestamp': datetime.utcnow().isoformat()
            }
            results.append(result)
        
        return jsonify({
            'success': True,
            'processed': len(results),
            'results': results,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Bulk processing failed: {e}")
        return jsonify({'error': 'Bulk processing failed', 'details': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8081))
    debug = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
    
    logger.info(f"Starting PropBolt Data API on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)

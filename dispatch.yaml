# PropBolt Enterprise - App Engine Dispatch Configuration
# Routes domains to auto-scaling services with load balancing
# Priority: API > Data API > Landing

dispatch:
  # HIGHEST PRIORITY: Main API Service (api.propbolt.com)
  # Enterprise-grade auto-scaling and load balancing
  - url: "api.propbolt.com/*"
    service: api

  # HIGH PRIORITY: Data API Service (data.propbolt.com)
  # Python-based data processing and analytics API
  - url: "data.propbolt.com/*"
    service: data

  # LOW PRIORITY: Landing Page (propbolt.com)
  # Marketing site with basic scaling
  - url: "propbolt.com/*"
    service: default

  # WWW redirect to main domain
  - url: "www.propbolt.com/*"
    service: default

  # Default fallback for any unmatched domains
  - url: "*/*"
    service: default
